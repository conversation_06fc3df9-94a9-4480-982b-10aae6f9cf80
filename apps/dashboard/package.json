{"name": "dashboard", "version": "0.0.0", "type": "module", "private": true, "packageManager": "pnpm@9.15.4", "scripts": {"dev": "next dev --turbopack --port 3000", "build": "next build", "start": "next start", "lint": "next lint", "typecheck": "tsc --noEmit"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@prisma/client": "^6.9.0", "@repo/auth": "workspace:*", "@repo/database": "workspace:*", "@repo/email": "workspace:*", "@repo/ui": "workspace:*", "@tanstack/react-query": "^5.81.5", "@tanstack/react-query-devtools": "^5.81.5", "@tanstack/react-table": "^8.21.3", "ag-grid-community": "^34.1.1", "ag-grid-react": "^34.1.1", "date-fns": "^4.1.0", "lottie-react": "^2.4.1", "lucide-react": "^0.525.0", "motion": "^12.23.11", "next": "^15.4.6", "next-auth": "5.0.0-beta.25", "react": "^19.1.1", "react-dom": "^19.1.1", "react-hook-form": "^7.60.0", "react-hotkeys-hook": "^5.1.0", "sonner": "^2.0.6", "zod": "^3.25.76", "zustand": "^5.0.6"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/prettier-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@tailwindcss/postcss": "^4.1.11", "@tanstack/eslint-plugin-query": "^5.81.2", "@types/node": "^22.15.3", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "eslint": "^9.30.0", "postcss": "^8.5.6", "tailwindcss": "^4.1.11", "typescript": "5.8.2"}, "prettier": "@repo/prettier-config"}