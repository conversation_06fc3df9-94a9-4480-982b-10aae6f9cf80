/** @type {import('next').NextConfig} */

const INTERNAL_PACKAGES = [
  "@repo/auth",
  "@repo/database",
  "@repo/email",
  "@repo/ui"
];
const nextConfig = {
  transpilePackages: INTERNAL_PACKAGES,
  outputFileTracingIncludes: {
    "/": ["../../packages/database/generated/**/*"]
  },

  async redirects() {
    return [
      {
        source: "/home",
        destination: "/",
        permanent: false
      },
      {
        source: "/verify-email",
        destination: "/sign-in",
        permanent: false
      },
      {
        source: "/accept-invitation",
        destination: "/sign-in",
        permanent: false
      },
      {
        source: "/onboarding",
        destination: "/sign-in",
        permanent: false
      },
      {
        source: "/:slug/settings",
        destination: "/:slug/settings/general",
        permanent: true
      }
    ];
  },

  async rewrites() {
    // In production, frontend will be at app.centaly.com and backend at api.centaly.com
    if (process.env.NODE_ENV === "development") {
      const apiUrl = process.env.API_URL || "http://localhost:3001";
      return [
        {
          source: "/api/:path*",
          destination: `${apiUrl}/api/:path*`
        }
      ];
    }
    return [];
  }
};

export default nextConfig;
