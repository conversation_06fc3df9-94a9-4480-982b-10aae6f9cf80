"use client";

import { useEffect, useState } from "react";
import { getActiveOrganization, type ActiveOrganization } from "@/src/lib/api/onboarding";

interface UseActiveOrganizationReturn {
  activeOrganization: ActiveOrganization | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export function useActiveOrganization(): UseActiveOrganizationReturn {
  const [activeOrganization, setActiveOrganization] = useState<ActiveOrganization | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchActiveOrganization = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await getActiveOrganization();
      setActiveOrganization(response.activeOrganization);
    } catch (err) {
      console.error("Error fetching active organization:", err);
      setError(err instanceof Error ? err.message : "Failed to fetch active organization");
      setActiveOrganization(null);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchActiveOrganization();
  }, []);

  return {
    activeOrganization,
    loading,
    error,
    refetch: fetchActiveOrganization
  };
}