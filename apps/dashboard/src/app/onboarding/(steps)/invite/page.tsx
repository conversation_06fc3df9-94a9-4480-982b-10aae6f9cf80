import * as React from "react";
import { Metadata } from "next";
import InviteOnboardingForm from "@/src/features/auth/components/invite-onboarding-form";
import { InviteOnboardingGuard } from "@/src/features/auth/components/invite-onboarding-guard";
import { AuthContainer } from "@repo/ui/components/auth-container";

export const metadata: Metadata = {
  title: "Invite Team | Centaly"
};

export default async function InviteOnboardingPage() {
  return (
    <InviteOnboardingGuard>
      <AuthContainer maxWidth="md">
        <InviteOnboardingForm />
      </AuthContainer>
    </InviteOnboardingGuard>
  );
}
