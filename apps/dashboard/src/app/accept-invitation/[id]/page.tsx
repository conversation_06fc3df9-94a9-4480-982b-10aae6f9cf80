import { Metadata } from "next";
import { redirect } from "next/navigation";
import Invitation<PERSON><PERSON><PERSON> from "@/src/features/auth/components/invitation-handler";
import { getBackendSession } from "@/src/features/auth/utils/session";
import { validateInvitation } from "@/src/lib/api/invitations";

export const metadata: Metadata = {
  title: "Accept Invitation | Centaly"
};

interface PageProps {
  params: Promise<{ id: string }>;
}

export default async function AcceptInvitationPage({ params }: PageProps) {
  const { id } = await params;

  const session = await getBackendSession();
  const validationResult = await validateInvitation(id);

  if (!validationResult.valid) {
    redirect("/");
  }

  return (
    <InvitationHandler
      invitationId={id}
      currentUser={session?.user || null}
    />
  );
}
