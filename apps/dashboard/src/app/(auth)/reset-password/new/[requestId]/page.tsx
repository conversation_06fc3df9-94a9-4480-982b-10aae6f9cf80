import * as React from "react";
import { Metada<PERSON> } from "next";
import { AuthContainer } from "@repo/ui/components/auth-container";
import NewPasswordForm from "@/src/features/auth/components/new-password-form";

export const metadata: Metadata = {
  title: "Set New Password | Centaly"
};

interface NewPasswordPageProps {
  params: Promise<{
    requestId: string;
  }>;
}

export default async function NewPasswordPage({ params }: NewPasswordPageProps) {
  const { requestId } = await params;

  return (
    <AuthContainer maxWidth="md">
      <NewPasswordForm requestId={requestId} />
    </AuthContainer>
  );
}