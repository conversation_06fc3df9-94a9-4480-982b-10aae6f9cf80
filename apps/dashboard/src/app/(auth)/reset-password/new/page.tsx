import { redirect } from "next/navigation";

export const dynamic = "force-dynamic";

interface NewPasswordPageProps {
  searchParams: Promise<{
    requestId?: string;
  }>;
}

export default async function NewPasswordPage({
  searchParams
}: NewPasswordPageProps) {
  const { requestId } = await searchParams;

  // If requestId is provided as a search parameter, redirect to the proper dynamic route
  if (requestId) {
    redirect(`/reset-password/new/${requestId}`);
  }

  // If no requestId is provided, this is likely an invalid access
  // Redirect to the reset password request page
  redirect("/reset-password");
}
