import * as React from "react";
import { Metada<PERSON> } from "next";
import { AuthPageGuard } from "@/src/features/auth/components/auth-page-guard";
import SignUpForm from "@/src/features/auth/components/sign-up-form";
import { AuthContainer } from "@repo/ui/components/auth-container";

export const metadata: Metadata = {
  title: "Sign Up | Centaly"
};

export default async function SignUpPage() {
  return (
    <AuthPageGuard>
      <AuthContainer maxWidth="md">
        <SignUpForm />
      </AuthContainer>
    </AuthPageGuard>
  );
}
