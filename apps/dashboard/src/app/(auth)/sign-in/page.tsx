import * as React from "react";
import { Metada<PERSON> } from "next";
import { AuthPageGuard } from "@/src/features/auth/components/auth-page-guard";
import SignInForm from "@/src/features/auth/components/sign-in-form";
import { AuthContainer } from "@repo/ui/components/auth-container";

export const metadata: Metadata = {
  title: "Sign In | Centaly"
};

export default async function SignInPage() {
  return (
    <AuthPageGuard>
      <AuthContainer maxWidth="md">
        <SignInForm />
      </AuthContainer>
    </AuthPageGuard>
  );
}
