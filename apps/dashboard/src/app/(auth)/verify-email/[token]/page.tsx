import { Metadata } from "next";
import { notFound, redirect } from "next/navigation";
import { verifyEmailWithToken } from "@/src/features/auth/actions/verify-email-with-token";
import EmailVerificationHandler from "@/src/features/auth/components/email-verification-handler";

export const metadata: Metadata = {
  title: "Email Verification | Centaly"
};

interface EmailVerificationPageProps {
  params: Promise<{ token: string }>;
}

export default async function EmailVerificationPage({
  params
}: EmailVerificationPageProps) {
  const { token } = await params;

  if (!token) {
    return notFound();
  }

  // Call the verification action and get the result
  const result = await verifyEmailWithToken({ token });

  // If verification failed, redirect to the appropriate page
  if (!result.success) {
    redirect(result.redirectUrl);
  }

  // If verification succeeded, use the client component for auto sign-in with the provider token
  return <EmailVerificationHandler providerToken={result.providerToken} />;
}
