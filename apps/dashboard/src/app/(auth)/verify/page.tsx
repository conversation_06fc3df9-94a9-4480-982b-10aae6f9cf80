import * as React from "react";
import { redirect } from "next/navigation";
import VerifyForm from "@/src/features/auth/components/verify-form";
import { prisma } from "@repo/database/client";
import { AuthContainer } from "@repo/ui/components/auth-container";

export default async function VerifyEmailPage({
  searchParams
}: {
  searchParams: Promise<{ email?: string; error?: string }>;
}): Promise<React.JSX.Element> {
  // Await the searchParams object before accessing its properties
  const params = await searchParams;

  if (!params?.email) {
    redirect("/sign-in");
  }

  const email = decodeURIComponent(params.email);

  // Get the user record to check email verification status
  const user = await prisma.user.findUnique({
    where: {
      email: email
    },
    select: {
      id: true,
      email: true,
      emailVerified: true
    }
  });

  // If no user found or email is already verified, redirect to sign in
  if (!user || user.emailVerified) {
    redirect("/sign-in");
  }
  return (
    <AuthContainer maxWidth="md">
      <VerifyForm error={params.error} />
    </AuthContainer>
  );
}
