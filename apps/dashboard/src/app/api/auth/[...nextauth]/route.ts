import { NextRequest } from "next/server";
import { handlers } from "@repo/auth";

export const GET = async (req: NextRequest) => {
  try {
    console.log("AUTH GET handler called:", req.url);
    console.log("AUTH_SECRET exists:", !!process.env.AUTH_SECRET);
    const result = await handlers.GET(req as any);
    console.log("AUTH GET result status:", result.status);
    return result;
  } catch (error) {
    console.error("AUTH GET error:", error);
    throw error;
  }
};

export const POST = async (req: NextRequest) => {
  try {
    console.log("AUTH POST handler called:", req.url);
    console.log("AUTH_SECRET exists:", !!process.env.AUTH_SECRET);
    const result = await handlers.POST(req as any);
    console.log("AUTH POST result status:", result.status);
    return result;
  } catch (error) {
    console.error("AUTH POST error:", error);
    throw error;
  }
};
