import { Metadata } from "next";
import { WorkspaceGuard } from "@/src/features/auth/components/workspace-guard";

export const metadata: Metadata = {
  title: "Home | Centaly"
};

interface PageProps {
  params: Promise<{ slug: string }>;
}

export default async function HomePage({ params }: PageProps) {
  const { slug } = await params;

  return (
    <WorkspaceGuard slug={slug}>
      <div>{/* Your home page content */}</div>
    </WorkspaceGuard>
  );
}
