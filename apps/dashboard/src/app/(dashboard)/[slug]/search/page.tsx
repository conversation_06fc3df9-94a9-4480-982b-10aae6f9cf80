import { Metadata } from "next";
import { WorkspaceGuard } from "@/src/features/auth/components/workspace-guard";

export const metadata: Metadata = {
  title: "Search | Centaly"
};

interface PageProps {
  params: Promise<{ slug: string }>;
}

export default async function SearchPage({ params }: PageProps) {
  const { slug } = await params;

  return (
    <WorkspaceGuard slug={slug}>
      <div className="flex flex-col p-6">
        <h1>Search</h1>
      </div>
    </WorkspaceGuard>
  );
}
