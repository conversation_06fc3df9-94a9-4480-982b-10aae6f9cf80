import { Metadata } from "next";
import { WorkspaceGuard } from "@/src/features/auth/components/workspace-guard";

export const metadata: Metadata = {
  title: "Inbox | Centaly"
};

interface PageProps {
  params: Promise<{ slug: string }>;
}

export default async function InboxPage({ params }: PageProps) {
  const { slug } = await params;

  return (
    <WorkspaceGuard slug={slug}>
      <div>{/* Your inbox page content */}</div>
    </WorkspaceGuard>
  );
}
