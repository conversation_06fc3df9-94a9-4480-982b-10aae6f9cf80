import { Metada<PERSON> } from "next";
import { WorkspaceGuard } from "@/src/features/auth/components/workspace-guard";
import WorkspaceDelete from "@/src/features/settings-general/components/workspace-delete";
import WorkspaceImage from "@/src/features/settings-general/components/workspace-image";
import WorkspaceName from "@/src/features/settings-general/components/workspace-name";
import { ScrollArea } from "@repo/ui/components/scroll-area";
import { Separator } from "@repo/ui/components/separator";

export const metadata: Metadata = {
  title: "General Settings | Centaly"
};

interface PageProps {
  params: Promise<{ slug: string }>;
}

export default async function GeneralSettingsPage({ params }: PageProps) {
  const { slug } = await params;

  return (
    <WorkspaceGuard slug={slug}>
      <div
        aria-label="General Settings Layout"
        className="relative h-[calc(100vh-3.5rem)]"
      >
        <ScrollArea className="h-full">
          <div className="flex flex-col max-w-[768px] px-6 mx-auto">
            <div className="py-18 space-y-6">
              <div
                aria-label="Header Section"
                className="gap-3"
              >
                <h1 className="text-2xl">General Settings</h1>
                <p className="text-muted-foreground text-sm">
                  Manage your workspace settings.
                </p>
              </div>
              <Separator className="my-4" />
              <div
                aria-label="Content Section"
                className="flex flex-col mt-10 gap-12"
              >
                <WorkspaceImage />
                <WorkspaceName />
                <WorkspaceDelete />
              </div>
            </div>
          </div>
        </ScrollArea>
      </div>
    </WorkspaceGuard>
  );
}
