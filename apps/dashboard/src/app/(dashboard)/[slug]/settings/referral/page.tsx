import { Metadata } from "next";
import { WorkspaceGuard } from "@/src/features/auth/components/workspace-guard";

export const metadata: Metadata = {
  title: "Referral Settings | Centaly"
};

interface PageProps {
  params: Promise<{ slug: string }>;
}

export default async function ReferralSettingsPage({ params }: PageProps) {
  const { slug } = await params;

  return (
    <WorkspaceGuard slug={slug}>
      <div className="p-10">
        <h1>Referral Program</h1>
      </div>
    </WorkspaceGuard>
  );
}
