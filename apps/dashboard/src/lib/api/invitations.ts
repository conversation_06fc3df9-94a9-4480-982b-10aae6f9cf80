import { apiGet, apiPost } from "@/src/lib/config/api";

// Type definitions for invitation API responses
export interface InvitationDetails {
  id: string;
  organizationName: string;
  organizationSlug: string;
  inviterEmail: string;
  email: string;
  role: "viewer" | "contributor" | "admin" | "owner";
  status: "pending" | "accepted" | "rejected" | "revoked" | "expired";
  expiresAt: string;
  organizationId: string;
  inviterId: string;
}

export interface InvitationResponse {
  success: boolean;
  message: string;
}

export interface InvitationValidation {
  valid: boolean;
  message?: string;
  email?: string;
  organizationName?: string;
  inviterName?: string;
}

// API response wrapper types that match the expected structure from invitation handler
export interface GetInvitationResponse {
  data?: InvitationDetails;
  error?: {
    message: string;
  };
}

export interface AcceptInvitationResponse {
  data?: InvitationResponse;
  error?: {
    message: string;
  };
}

export interface RejectInvitationResponse {
  data?: InvitationResponse;
  error?: {
    message: string;
  };
}

// Get invitation details (authenticated endpoint)
export async function getInvitation({
  query
}: {
  query: { id: string };
}): Promise<GetInvitationResponse> {
  try {
    const response = await apiGet(`/api/v1/invitations/${query.id}`);

    if (response.ok) {
      const data = await response.json();
      return { data };
    } else {
      const errorData = await response.json();
      return {
        error: {
          message:
            errorData.error || `Failed to fetch invitation: ${response.status}`
        }
      };
    }
  } catch (error) {
    console.error("Error fetching invitation:", error);
    return {
      error: {
        message: "Failed to fetch invitation details"
      }
    };
  }
}

// Accept invitation (authenticated endpoint)
export async function acceptInvitation({
  invitationId
}: {
  invitationId: string;
}): Promise<AcceptInvitationResponse> {
  try {
    const response = await apiPost(
      `/api/v1/invitations/${invitationId}/accept`
    );

    if (response.ok) {
      const data = await response.json();
      return { data };
    } else {
      const errorData = await response.json();
      return {
        error: {
          message:
            errorData.error || `Failed to accept invitation: ${response.status}`
        }
      };
    }
  } catch (error) {
    console.error("Error accepting invitation:", error);
    return {
      error: {
        message: "Failed to accept invitation"
      }
    };
  }
}

// Reject invitation (authenticated endpoint)
export async function rejectInvitation({
  invitationId
}: {
  invitationId: string;
}): Promise<RejectInvitationResponse> {
  try {
    const response = await apiPost(
      `/api/v1/invitations/${invitationId}/reject`
    );

    if (response.ok) {
      const data = await response.json();
      return { data };
    } else {
      const errorData = await response.json();
      return {
        error: {
          message:
            errorData.error || `Failed to reject invitation: ${response.status}`
        }
      };
    }
  } catch (error) {
    console.error("Error rejecting invitation:", error);
    return {
      error: {
        message: "Failed to reject invitation"
      }
    };
  }
}

// Validate invitation (public endpoint - used in validateInvitation function)
export async function validateInvitation(
  invitationId: string
): Promise<InvitationValidation> {
  try {
    const response = await apiGet(
      `/api/v1/invitations/${invitationId}/validate`
    );

    if (response.ok) {
      return await response.json();
    } else {
      return {
        valid: false,
        message: "Failed to validate invitation"
      };
    }
  } catch (error) {
    console.error("Error validating invitation:", error);
    return {
      valid: false,
      message: "Failed to validate invitation"
    };
  }
}
