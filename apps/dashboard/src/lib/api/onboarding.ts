import { apiGet, apiPatch, apiPost } from "@/src/lib/config/api";

export async function updateOnboardingStatus(
  status: "incomplete" | "workspace" | "invite" | "complete"
) {
  const response = await apiPatch("/api/v1/profile/onboarding-status", {
    onboardingStatus: status
  });

  if (!response.ok) {
    throw new Error(`Failed to update onboarding status: ${response.status}`);
  }

  return response.json();
}

export async function updateDefaultWorkspace(workspaceSlug: string | null) {
  const response = await apiPatch("/api/v1/profile/default-workspace", {
    defaultWorkspace: workspaceSlug
  });

  if (!response.ok) {
    throw new Error(`Failed to update default workspace: ${response.status}`);
  }

  return response.json();
}

export async function checkSlugAvailability(slug: string) {
  const response = await apiPost("/api/v1/organization/check-slug", {
    slug
  });

  if (!response.ok) {
    throw new Error(`Failed to check slug availability: ${response.status}`);
  }

  return response.json();
}

export async function createWorkspace(name: string, slug: string) {
  const response = await apiPost("/api/v1/organization/create", {
    name,
    slug
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || `Failed to create workspace: ${response.status}`);
  }

  return response.json();
}

export async function getUserOrganizations() {
  const response = await apiGet("/api/v1/profile/organizations");

  if (!response.ok) {
    throw new Error(`Failed to get user organizations: ${response.status}`);
  }

  return response.json();
}

export async function sendInvitations(
  organizationSlug: string,
  invitations: Array<{
    email: string;
    role: "viewer" | "contributor" | "admin" | "owner";
  }>
) {
  const response = await apiPost(`/api/v1/organizations/${organizationSlug}/invitations`, {
    invitations
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || `Failed to send invitations: ${response.status}`);
  }

  return response.json();
}

export interface ActiveOrganization {
  id: string;
  name: string;
  slug: string;
  logo: string | null;
  userRole: "viewer" | "contributor" | "admin" | "owner";
  createdAt: string;
}

export async function getActiveOrganization() {
  const response = await apiGet("/api/v1/profile/active-organization");

  if (!response.ok) {
    throw new Error(`Failed to get active organization: ${response.status}`);
  }

  return response.json() as Promise<{ activeOrganization: ActiveOrganization | null }>;
}
