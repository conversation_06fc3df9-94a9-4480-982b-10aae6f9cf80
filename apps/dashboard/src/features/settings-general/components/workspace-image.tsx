/* eslint-disable @next/next/no-img-element */
"use client";

import { useEffect, useState } from "react";
import {
  deleteWorkspaceLogo,
  uploadWorkspaceImage
} from "@/src/lib/api/workspace-image";
import { Spinner } from "@repo/ui/components/spinner";
import {
  useFileUpload,
  type FileWithPreview
} from "@repo/ui/hooks/use-file-upload";
import { useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import { useWorkspace } from "../../auth/hooks/use-workspace";

export default function WorkspaceImage() {
  const { workspace } = useWorkspace();
  const queryClient = useQueryClient();
  const [isUploading, setIsUploading] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [displayImageUrl, setDisplayImageUrl] = useState<string | null>(null);

  // Sync displayImageUrl with workspace.logo when workspace data changes
  useEffect(() => {
    setDisplayImageUrl(workspace?.logo || null);
  }, [workspace?.logo]);

  const [
    { files },
    {
      removeFile,
      openFileDialog,
      getInputProps,
      handleDragEnter,
      handleDragLeave,
      handleDragOver,
      handleDrop
    }
  ] = useFileUpload({
    accept: "image/png,image/jpeg,image/jpg,image/gif",
    maxSize: 10 * 1024 * 1024, // 10MB
    multiple: false,
    onFilesAdded: handleFilesAdded
  });

  async function handleFilesAdded(addedFiles: FileWithPreview[]) {
    if (!workspace) return;
    const file = addedFiles[0];
    if (!file?.file || !(file.file instanceof File)) return;

    setIsUploading(true);
    setErrorMessage(null); // Clear any previous errors

    try {
      // Upload to S3 and update database - this returns the S3 URL
      const logoUrl = await uploadWorkspaceImage(workspace.slug!, file.file);
      toast.success("Workspace image updated successfully");

      // Update our display to show the uploaded S3 URL
      setDisplayImageUrl(logoUrl);

      // Invalidate all workspace-related caches to ensure fresh data on next fetch
      queryClient.invalidateQueries({
        queryKey: ["workspace", workspace.slug]
      });
      queryClient.invalidateQueries({
        queryKey: ["organizations"]
      });
      queryClient.invalidateQueries({
        queryKey: ["activeOrganization"]
      });
    } catch (error) {
      console.error("Upload failed:", error);
      const errorMsg =
        error instanceof Error
          ? error.message
          : "Failed to upload image, please try again.";
      setErrorMessage(errorMsg);
      toast.error(errorMsg);
    } finally {
      setIsUploading(false);
      // Clear the file from the upload hook after upload attempt
      if (files.length > 0 && files[0]?.id) {
        removeFile(files[0].id);
      }
    }
  }

  async function handleDeleteLogo() {
    if (!workspace) return;
    setIsDeleting(true);
    setErrorMessage(null); // Clear any previous errors

    try {
      await deleteWorkspaceLogo(workspace.slug!);
      toast.success("Workspace image deleted successfully");

      // Clear the display image
      setDisplayImageUrl(null);

      // Invalidate all workspace-related caches to ensure fresh data on next fetch
      queryClient.invalidateQueries({
        queryKey: ["workspace", workspace.slug]
      });
      queryClient.invalidateQueries({
        queryKey: ["organizations"]
      });
      queryClient.invalidateQueries({
        queryKey: ["activeOrganization"]
      });

      // Clear any files from the upload hook to prevent showing stale previews
      if (files.length > 0 && files[0]?.id) {
        removeFile(files[0].id);
      }
    } catch (error) {
      console.error("Delete failed:", error);
      const errorMsg =
        error instanceof Error
          ? error.message
          : "Failed to delete image, please try again.";
      setErrorMessage(errorMsg);
      toast.error(errorMsg);
    } finally {
      setIsDeleting(false);
    }
  }

  // Simple display logic: show the display image URL or file preview during upload
  const previewUrl = displayImageUrl || files[0]?.preview || null;
  const hasLogo = !!previewUrl;
  const isProcessing = isUploading || isDeleting;

  // TODO: Implement proper loading state
  if (!workspace) {
    return (
      <div className="flex items-center gap-8 border rounded-2xl p-6 h-[130px]">
        <div className="flex items-center gap-2">
          <Spinner size="small" />
          <p className="text-sm text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex items-center gap-8 border rounded-2xl p-6">
      <div className="relative inline-flex">
        {/* Drop area */}
        <button
          className="border-input hover:bg-accent/50 data-[dragging=true]:bg-accent/50 focus-visible:border-ring focus-visible:ring-ring/50 relative flex size-20 items-center justify-center overflow-hidden rounded-full border border-dashed transition-colors outline-none focus-visible:ring-[3px] has-disabled:pointer-events-none has-disabled:opacity-50 has-[img]:border-none disabled:pointer-events-none disabled:opacity-50"
          onClick={openFileDialog}
          onDragEnter={handleDragEnter}
          onDragLeave={handleDragLeave}
          onDragOver={handleDragOver}
          onDrop={handleDrop}
          disabled={isProcessing}
          aria-label={
            hasLogo ? "Change workspace image" : "Upload workspace image"
          }
        >
          {previewUrl ? (
            <img
              className="size-full object-cover"
              src={previewUrl}
              alt="Workspace logo"
              width={80}
              height={80}
              style={{ objectFit: "cover" }}
            />
          ) : (
            <div className="text-center">
              <div className="text-muted-foreground text-xs">Upload</div>
            </div>
          )}

          {isProcessing && (
            <div className="absolute inset-0 flex items-center justify-center bg-black/50 rounded-full">
              <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
            </div>
          )}
        </button>

        {/* Delete button */}
        {hasLogo && !isProcessing && (
          <button
            onClick={handleDeleteLogo}
            className="border-background focus-visible:border-background absolute -top-1 -right-1 size-6 rounded-full border-2 bg-neutral-800 text-white hover:bg-neutral-700 transition-colors shadow-none flex items-center justify-center"
            aria-label="Remove workspace image"
          >
            <svg
              className="size-3.5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        )}

        <input
          {...getInputProps()}
          className="sr-only"
          aria-label="Upload workspace image file"
          tabIndex={-1}
          disabled={isProcessing}
        />
      </div>

      <div className="flex flex-col gap-1">
        <p className="text-sm font-medium">Workspace Image</p>
        <p className="text-sm text-muted-foreground">
          Click to upload or drag and drop your logo. <br />
          We only support PNGs, JPEGs and GIFs under 10MB.
        </p>
        {errorMessage && <p className="text-sm text-red-500">{errorMessage}</p>}
      </div>
    </div>
  );
}
