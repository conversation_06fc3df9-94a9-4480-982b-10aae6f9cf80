"use client";

import { apiPut } from "@/src/lib/config/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

interface UpdateWorkspaceNameData {
  organizationSlug: string;
  name: string;
}

export function useUpdateWorkspaceName() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ organizationSlug, name }: UpdateWorkspaceNameData) => {
      const response = await apiPut(
        `/api/v1/organizations/${organizationSlug}`,
        {
          name
        }
      );

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.error ||
            `Failed to update workspace name: ${response.status}`
        );
      }

      return response.json();
    },
    onSuccess: () => {
      // Invalidate and refetch organization queries
      queryClient.invalidateQueries({ queryKey: ["organizations"] });
      queryClient.invalidateQueries({ queryKey: ["activeOrganization"] });
      queryClient.invalidateQueries({ queryKey: ["workspace"] });

      toast.success("Workspace name updated successfully");
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to update workspace name");
    }
  });
}
