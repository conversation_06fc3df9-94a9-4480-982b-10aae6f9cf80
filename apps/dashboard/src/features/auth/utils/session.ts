import { cookies } from "next/headers";

interface SessionUser {
  id: string;
  email: string;
  name: string;
  emailVerified?: string | null;
  onboardingStatus?: "incomplete" | "workspace" | "invite" | "complete";
  defaultWorkspace?: string | null;
  role?: string;
}

interface SessionData {
  user: SessionUser;
  expires?: string;
}

/**
 * Gets the current session by validating with the backend API
 * This ensures session compatibility between Auth.js frontend and Hono backend
 * @returns Session data name, id, email or null if no session is found
 */
export async function getBackendSession(): Promise<SessionData | null> {
  try {
    const cookieStore = await cookies();
    const sessionCookie = cookieStore.get("next-auth.session-token");

    if (!sessionCookie) {
      return null;
    }

    const response = await fetch("http://localhost:3001/api/auth/session", {
      headers: {
        Cookie: `${sessionCookie.name}=${sessionCookie.value}`
      }
    });

    if (!response.ok) {
      return null;
    }

    const sessionData = await response.json();
    return sessionData;
  } catch (error) {
    console.error("Error fetching session from backend:", error);
    return null;
  }
}

/**
 * Type guard to check if session has a valid user
 */
export function hasValidUser(
  session: SessionData | null
): session is SessionData & { user: SessionUser } {
  return session?.user?.id !== undefined;
}
