/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import React, { useState } from "react";
import {
  getUserOrganizations,
  sendInvitations,
  updateOnboardingStatus
} from "@/src/lib/api/onboarding";
import { zodResolver } from "@hookform/resolvers/zod";
import { AuthContainer } from "@repo/ui/components/auth-container";
import { Button } from "@repo/ui/components/button";
import { Card } from "@repo/ui/components/card";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormProvider
} from "@repo/ui/components/form";
import { Input } from "@repo/ui/components/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@repo/ui/components/select";
import { useForm } from "react-hook-form";
import { z } from "zod";

import OnboardingCompletionLoader from "./onboarding-completion-loader";

const roleOptions = [
  { value: "viewer", label: "Viewer" },
  { value: "contributor", label: "Contributor" },
  { value: "admin", label: "Admin" }
] as const;

const inviteSchema = z.object({
  invites: z
    .array(
      z.object({
        email: z
          .string()
          .email("Invalid email address")
          .optional()
          .or(z.literal("")),
        role: z.enum(["viewer", "contributor", "admin"]).optional()
      })
    )
    .length(3)
    .refine(
      (invites) => {
        const validEmails = invites.filter(
          (invite) => invite.email && invite.email.trim() !== ""
        );
        return validEmails.length > 0;
      },
      {
        message: "Please add at least one email address to continue",
        path: ["invites"]
      }
    )
});

type InviteFormData = z.infer<typeof inviteSchema>;

const InviteOnboardingForm = () => {
  const [loadingState, setLoadingState] = useState<boolean>(false);
  const [showCompletionLoader, setShowCompletionLoader] =
    useState<boolean>(false);
  const [error, setError] = useState<string>("");

  const methods = useForm<InviteFormData>({
    resolver: zodResolver(inviteSchema),
    defaultValues: {
      invites: [
        { email: "", role: "contributor" },
        { email: "", role: "contributor" },
        { email: "", role: "contributor" }
      ]
    },
    mode: "onSubmit"
  });

  const onSubmit = async (data: InviteFormData) => {
    try {
      setLoadingState(true);
      setError("");

      // Filter out empty emails and prepare invitation data
      const validInvites = data.invites
        .filter((invite) => invite.email && invite.email.trim() !== "")
        .map((invite) => ({
          email: invite.email!.trim(),
          // Map frontend roles to backend roles
          role:
            invite.role === "contributor"
              ? "contributor"
              : (invite.role as "viewer" | "contributor" | "admin")
        }));

      if (validInvites.length === 0) {
        setError("Please add at least one email address to continue");
        setLoadingState(false);
        return;
      }

      // Get user's current organization
      const organizations = await getUserOrganizations();
      if (
        !organizations.organizations ||
        organizations.organizations.length === 0
      ) {
        setError("No workspace found. Please contact support.");
        setLoadingState(false);
        return;
      }

      const currentOrg = organizations.organizations[0];

      // Send invitations
      await sendInvitations(currentOrg.slug, validInvites);

      // Update onboarding status to complete
      await updateOnboardingStatus("complete");

      // Show completion loader which will handle the redirect
      setLoadingState(false);
      setShowCompletionLoader(true);
    } catch (err: any) {
      console.error("Error sending invitations:", err);
      setError(err.message || "Failed to send invitations. Please try again.");
      setLoadingState(false);
    }
  };

  const clearErrorsOnChange = () => {
    if (error) {
      setError("");
    }
    // Clear form validation errors when user starts typing
    if (methods.formState.errors.invites) {
      methods.clearErrors("invites");
    }
  };

  const handleSkip = async () => {
    try {
      setLoadingState(true);
      // Mark onboarding as complete even when skipping
      await updateOnboardingStatus("complete");

      // Show completion loader which will handle the redirect
      setLoadingState(false);
      setShowCompletionLoader(true);
    } catch (err) {
      console.error("Error skipping onboarding:", err);
      setError("Failed to complete onboarding. Please try again.");
      setLoadingState(false);
    }
  };

  // Show completion loader when onboarding is complete
  if (showCompletionLoader) {
    return (
      <AuthContainer maxWidth="md">
        <div className="flex justify-center">
          <OnboardingCompletionLoader />
        </div>
      </AuthContainer>
    );
  }

  return (
    <main>
      <Card className="w-full max-w-lg p-6 space-y-6">
        <div>
          <h1 className="text-3xl pb-4">Invite team members</h1>
          <p className="text-muted-foreground">
            Add team members to collaborate on your workspace. You can skip this
            step and invite members later.
          </p>
        </div>

        {error && <p className="text-destructive">{error}</p>}

        <FormProvider {...methods}>
          <form
            onSubmit={methods.handleSubmit(onSubmit)}
            className="space-y-6"
          >
            <div className="space-y-4">
              {Array.from({ length: 3 }).map((_, index) => (
                <div
                  key={index}
                  className="flex gap-4"
                >
                  <FormField
                    control={methods.control}
                    name={`invites.${index}.email`}
                    render={({ field }) => (
                      <FormItem className="flex-1">
                        {index === 0 && <FormLabel>Email address</FormLabel>}
                        <FormControl>
                          <Input
                            {...field}
                            type="email"
                            placeholder="<EMAIL>"
                            maxLength={255}
                            autoCapitalize="off"
                            disabled={loadingState}
                            onChange={(e) => {
                              field.onChange(e);
                              clearErrorsOnChange();
                            }}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={methods.control}
                    name={`invites.${index}.role`}
                    render={({ field }) => (
                      <FormItem className="flex-shrink-0">
                        {index === 0 && <FormLabel>Role</FormLabel>}
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                          disabled={loadingState}
                        >
                          <FormControl>
                            <SelectTrigger className="w-32">
                              <SelectValue placeholder="Select role" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {roleOptions.map((option) => (
                              <SelectItem
                                key={option.value}
                                value={option.value}
                              >
                                {option.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              ))}

              {/* Display validation error for the invites array */}
              {methods.formState.errors.invites && (
                <p className="text-sm text-destructive">
                  {methods.formState.errors.invites.message}
                </p>
              )}
            </div>

            <div className="flex gap-3">
              <Button
                type="button"
                variant="outline"
                size="lg"
                className="flex-1"
                onClick={handleSkip}
                disabled={loadingState}
              >
                Skip for now
              </Button>
              <Button
                type="submit"
                size="lg"
                className="flex-1"
                loading={loadingState}
              >
                Continue
              </Button>
            </div>
          </form>
        </FormProvider>
      </Card>
    </main>
  );
};

export default InviteOnboardingForm;
