import { WorkspaceDAL } from "@/src/features/auth/dal/workspace-dal";

interface WorkspaceGuardProps {
  slug: string;
  children: React.ReactNode;
}

/**
 * Route Protection Component for Workspace Pages
 *
 * Validates that authenticated users have access to the requested workspace.
 * Redirects unauthorized users to their default workspace or sign-in page.
 */
export async function WorkspaceGuard({ slug, children }: WorkspaceGuardProps) {
  // Validate workspace access - will redirect if unauthorized
  await WorkspaceDAL.validateWorkspaceAccess(slug);

  return <>{children}</>;
}
