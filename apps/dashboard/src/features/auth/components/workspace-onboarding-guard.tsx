import { redirect } from "next/navigation";
import { AuthDAL } from "@/src/features/auth/dal/auth-dal";

interface WorkspaceOnboardingGuardProps {
  children: React.ReactNode;
}

/**
 * Route Protection Component for Workspace Onboarding Page
 *
 * This component implements the Data Access Layer (DAL) pattern for route-level
 * authorization as recommended in the Next.js documentation.
 *
 * It protects the workspace onboarding page by:
 * 1. Checking if the user is authenticated and email verified
 * 2. If not authenticated, redirecting to sign-in
 * 3. If email not verified, redirecting to verification
 * 4. If user has already completed onboarding, redirecting to home
 * 5. If user has already created a workspace (status "workspace" or "invite"), redirecting to invite page
 * 6. If user already has organizations, redirecting to invite page
 * 7. If all checks pass, allows access to workspace creation
 *
 * Access Requirements:
 * - User must be authenticated AND email verified
 * - User must have "incomplete" onboarding status
 * - User must not have any existing organizations
 *
 * Redirect Logic:
 * - If user has already completed onboarding → redirect to home page
 * - If user has already created a workspace → redirect to invite onboarding page
 * - If user already has organizations → redirect to invite onboarding page
 *
 * Usage:
 * ```tsx
 * export default async function WorkspaceOnboardingPage() {
 *   return (
 *     <WorkspaceOnboardingGuard>
 *       <AuthContainer maxWidth="md">
 *         <WorkspaceOnboardingForm />
 *       </AuthContainer>
 *     </WorkspaceOnboardingGuard>
 *   );
 * }
 * ```
 */
export async function WorkspaceOnboardingGuard({
  children
}: WorkspaceOnboardingGuardProps) {
  // Use the DAL to check if user should be redirected
  const authResult = await AuthDAL.checkWorkspaceOnboardingAccess();

  // If user should be redirected, perform the redirect
  if (authResult.shouldRedirect && authResult.redirectTo) {
    redirect(authResult.redirectTo);
  }

  // If user has proper access, render the workspace onboarding page
  return <>{children}</>;
}
