import { redirect } from "next/navigation";
import { AuthDAL } from "@/src/features/auth/dal/auth-dal";

interface AuthPageGuardProps {
  children: React.ReactNode;
}

/**
 * Route Protection Component for Authentication Pages
 * 
 * This component implements the Data Access Layer (DAL) pattern for route-level
 * authorization as recommended in the Next.js documentation.
 * 
 * It protects sign-in and sign-up pages by:
 * 1. Checking if the user is already authenticated
 * 2. If authenticated, redirecting them based on their status:
 *    - Incomplete onboarding → /onboarding/workspace
 *    - No organizations → /onboarding/workspace  
 *    - Has defaultWorkspace → /{defaultWorkspace}/home
 *    - Has active organization → /{activeOrganization.slug}/home
 *    - Has organizations → /{firstOrganization.slug}/home
 *    - Unverified email → /verify
 *    - Fallback → /
 * 3. If not authenticated, allows access to the auth page
 * 
 * Usage:
 * ```tsx
 * export default async function SignInPage() {
 *   return (
 *     <AuthPageGuard>
 *       <AuthContainer maxWidth="md">
 *         <SignInForm />
 *       </AuthContainer>
 *     </AuthPageGuard>
 *   );
 * }
 * ```
 */
export async function AuthPageGuard({ children }: AuthPageGuardProps) {
  // Use the DAL to check if user should be redirected
  const authResult = await AuthDAL.checkAuthPageAccess();

  // If user is authenticated and should be redirected, perform the redirect
  if (authResult.shouldRedirect && authResult.redirectTo) {
    redirect(authResult.redirectTo);
  }

  // If user is not authenticated or no redirect is needed, render the auth page
  return <>{children}</>;
}
