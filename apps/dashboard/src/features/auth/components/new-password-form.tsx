"use client";

import React, { useState, useTransition } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { resetPassword } from "@/src/features/auth/actions/reset-password";
import {
  newPasswordSchema,
  type NewPasswordFormData
} from "@/src/features/auth/schemas/new-password-schema";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@repo/ui/components/button";
import { Card } from "@repo/ui/components/card";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormProvider
} from "@repo/ui/components/form";
import { InputPassword } from "@repo/ui/components/input-password";
import { ArrowLeft } from "lucide-react";
import { useForm } from "react-hook-form";

interface NewPasswordFormProps {
  requestId: string;
}

const NewPasswordForm = ({
  requestId
}: NewPasswordFormProps): React.JSX.Element => {
  const [error, setError] = useState<string>("");
  const [fieldErrors, setFieldErrors] = useState<Record<string, string[]>>({});
  const [isPending, startTransition] = useTransition();
  const [isExpired, setIsExpired] = useState(false);
  const router = useRouter();

  // Initialize form with React Hook Form and Zod validation
  const methods = useForm<NewPasswordFormData>({
    resolver: zodResolver(newPasswordSchema),
    defaultValues: {
      password: "",
      confirmPassword: ""
    },
    mode: "onSubmit"
  });

  // We'll validate the reset request when the form is submitted
  // This simplifies the flow and reduces API calls

  // Form submit handler using server action
  const onSubmit = async (data: NewPasswordFormData) => {
    setError("");
    setFieldErrors({});

    startTransition(async () => {
      try {
        const result = await resetPassword({
          requestId,
          password: data.password,
          confirmPassword: data.confirmPassword
        });

        if (!result.success) {
          // Handle validation errors
          if (result.errors) {
            setFieldErrors(result.errors);
            // Set the first error as the main error message
            const firstError = Object.values(result.errors)[0]?.[0];
            if (firstError) {
              setError(firstError);
            }
          }
          if (result.message) {
            setError(result.message);
            // If it's an expired/invalid link error, show expired state
            if (
              result.message.includes("expired") ||
              result.message.includes("Invalid")
            ) {
              setIsExpired(true);
            }
          }
        } else {
          // Success - redirect to sign-in page
          router.push("/sign-in?message=password-reset-success");
        }
      } catch (error) {
        console.error("Password reset error:", error);
        setError("An unexpected error occurred. Please try again.");
      }
    });
  };

  // Clear errors when user starts typing
  const clearErrorsOnChange = () => {
    if (error) {
      setError("");
    }
    if (Object.keys(fieldErrors).length > 0) {
      setFieldErrors({});
    }
  };

  // Show expired/invalid link message
  if (isExpired) {
    return (
      <div>
        <div className="flex flex-col space-y-2 p-6 text-center">
          <h1 className="text-3xl pb-4">Link Expired</h1>
          <p className="text-muted-foreground">
            This password reset link has expired or is invalid. Please request a
            new one.
          </p>
        </div>
        <div className="p-6 pt-0 flex flex-col gap-4 text-center">
          <Link href="/reset-password">
            <Button className="w-full">Request New Reset Link</Button>
          </Link>
          <Link href="/sign-in">
            <Button
              variant="link"
              className="w-full"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Sign In
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <main className="flex items-center justify-center h-screen">
      <Card className="w-full max-w-lg p-6 space-y-6">
        <div>
          <h1 className="text-3xl pb-4">Set New Password</h1>
          <p className="text-muted-foreground">
            Enter your new password below to complete the reset process.
          </p>
        </div>

        <FormProvider {...methods}>
          <form
            onSubmit={methods.handleSubmit(onSubmit)}
            className="flex flex-col gap-4"
          >
            {error && <p className="text-sm text-destructive">{error}</p>}

            <FormField
              control={methods.control}
              name="password"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>New Password</FormLabel>
                  <FormControl>
                    <InputPassword
                      {...field}
                      maxLength={72}
                      autoComplete="new-password"
                      disabled={isPending}
                      onChange={(e) => {
                        field.onChange(e);
                        clearErrorsOnChange();
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={methods.control}
              name="confirmPassword"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Confirm Password</FormLabel>
                  <FormControl>
                    <InputPassword
                      {...field}
                      maxLength={72}
                      autoComplete="new-password"
                      disabled={isPending}
                      onChange={(e) => {
                        field.onChange(e);
                        clearErrorsOnChange();
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Button
              type="submit"
              disabled={isPending}
              size="lg"
              className="w-full"
              loading={isPending}
            >
              {isPending ? "Resetting Password..." : "Reset Password"}
            </Button>
          </form>
        </FormProvider>

        <div className="items-center  pt-0 text-muted-foreground flex justify-start gap-1">
          <Link
            className="hover:underline"
            href="/sign-in"
          >
            <ArrowLeft className="h-4 w-4 mr-2 inline" />
            Back to sign in
          </Link>
        </div>
      </Card>
    </main>
  );
};

export default NewPasswordForm;
