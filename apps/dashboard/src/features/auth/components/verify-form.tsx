"use client";

import React, { useEffect, useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { useSearchParams } from "next/navigation";
import { Button } from "@repo/ui/components/button";
import { Card } from "@repo/ui/components/card";
import { Skeleton } from "@repo/ui/components/skeleton";
import { AnimatePresence, motion } from "motion/react";

import { resendVerificationEmail } from "../actions/resend-verification-email";

interface VerifyFormProps {
  error?: string;
}

const VerifyForm = ({ error: initialError = undefined }: VerifyFormProps) => {
  const searchParams = useSearchParams();
  const [email, setEmail] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  // Get email from URL params and set initial error
  useEffect(() => {
    const emailParam = searchParams.get("email");
    if (emailParam) {
      setEmail(decodeURIComponent(emailParam));
    }

    // Set initial error message if provided
    if (initialError) {
      if (initialError === "expired") {
        setError(
          "Your verification link has expired. Please request a new one."
        );
      } else {
        setError(
          "There was an issue with your verification link. Please try again."
        );
      }
    }
  }, [searchParams, initialError]);

  const handleResendEmail = async () => {
    if (!email) {
      setError("Email not found. Please try signing up again.");
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const result = await resendVerificationEmail({ email });

      if (result.success) {
        setSuccess(true);
      } else {
        setError(
          result.message ||
            "Failed to resend verification email. Please try again."
        );
      }
    } catch (err) {
      console.error("Failed to resend verification email:", err);
      setError("Failed to resend verification email. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <main>
      <Card className="w-full max-w-lg p-6 space-y-6">
        <h1 className="text-3xl pb-4 text-center">Verify your email</h1>

        <div className="text-center flex items-center justify-center gap-2 text-muted-foreground">
          <AnimatePresence mode="wait">
            {email ? (
              <motion.span
                key="email-text"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0 }}
                transition={{
                  duration: 0.5,
                  ease: "easeOut"
                }}
              >
                We&apos;ve sent the confirmation to{" "}
                <span className="text-primary">{email}</span>
              </motion.span>
            ) : (
              <Skeleton className="w-full h-[24px]" />
            )}
          </AnimatePresence>
        </div>

        {error && <p className="text-destructive">{error}</p>}

        <div
          aria-label="Email providers"
          className="grid grid-cols-2 gap-3"
        >
          <Button
            variant="outline"
            size="lg"
            className="flex flex-row items-center gap-2 w-full h-10"
            onClick={() => {
              window.open("https://mail.google.com", "_blank");
            }}
          >
            <Image
              priority={true}
              src="/email-providers/gmail.svg"
              alt="Gmail"
              width={24}
              height={24}
            />
            <span className="font-semibold">Gmail</span>
          </Button>
          <Button
            variant="outline"
            className="flex flex-row items-center gap-2 w-full h-10"
            onClick={() => {
              window.open("https://mail.apple.com", "_blank");
            }}
          >
            <Image
              priority={true}
              src="/email-providers/apple-mail.svg"
              alt="Apple Mail"
              width={24}
              height={24}
            />
            <span className="font-semibold">Apple Mail</span>
          </Button>
          <Button
            variant="outline"
            className="flex flex-row items-center gap-2 w-full h-10"
            onClick={() => {
              window.open("https://outlook.com", "_blank");
            }}
          >
            <Image
              priority={true}
              src="/email-providers/outlook.svg"
              alt="Outlook"
              width={24}
              height={24}
            />
            <span className="font-semibold">Outlook</span>
          </Button>
          <Button
            variant="outline"
            className="flex flex-row items-center gap-2 w-full h-10"
            onClick={() => {
              window.open("https://mail.yahoo.com", "_blank");
            }}
          >
            <Image
              priority={true}
              src="/email-providers/yahoo.svg"
              alt="Yahoo"
              width={24}
              height={24}
            />
            <span className="font-semibold">Yahoo!</span>
          </Button>
        </div>

        {success && (
          <p className="text-sm text-muted-foreground">
            We resent your verification email to{" "}
            <span className="text-primary">{email || "your email"}</span>. If
            you don&apos;t see it in your inbox, please check your spam folder.
            If you are still having problems, please{" "}
            <Link
              href="#"
              className="text-primary hover:underline"
            >
              contact support
            </Link>{" "}
            or{" "}
            <button
              className="text-primary hover:underline disabled:opacity-50"
              onClick={handleResendEmail}
              disabled={isLoading}
            >
              {isLoading ? "Resending..." : "try again"}
            </button>
            .
          </p>
        )}

        <div className="space-y-4">
          {!success && (
            <div className="flex items-center justify-center gap-2">
              <p className="text-sm text-muted-foreground">
                Didn&apos;t receive the email?
              </p>
              <button
                className="text-sm hover:text-primary-violet hover:underline disabled:opacity-50"
                onClick={handleResendEmail}
                disabled={isLoading}
              >
                {isLoading ? "Resending..." : "Resend verification email"}
              </button>
            </div>
          )}
        </div>
      </Card>
    </main>
  );
};

export default VerifyForm;
