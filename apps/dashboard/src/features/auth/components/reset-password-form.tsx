"use client";

import React, { useState, useTransition } from "react";
import Link from "next/link";
import { sendResetPasswordInstructions } from "@/src/features/auth/actions/send-reset-password-instructions";
import {
  sendResetPasswordInstructionsSchema,
  type SendResetPasswordInstructionsFormData
} from "@/src/features/auth/schemas/send-reset-password-instructions-schema";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@repo/ui/components/button";
import { Card } from "@repo/ui/components/card";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormProvider
} from "@repo/ui/components/form";
import { Input } from "@repo/ui/components/input";
import { ArrowLeft } from "lucide-react";
import { useForm } from "react-hook-form";

const ResetPasswordForm = () => {
  const [error, setError] = useState<string>("");
  const [fieldErrors, setFieldErrors] = useState<Record<string, string[]>>({});
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isPending, startTransition] = useTransition();

  // Initialize form with React Hook Form and Zod validation
  const methods = useForm<SendResetPasswordInstructionsFormData>({
    resolver: zodResolver(sendResetPasswordInstructionsSchema),
    defaultValues: {
      email: ""
    },
    mode: "onSubmit"
  });

  // Form submit handler using server action
  const onSubmit = async (data: SendResetPasswordInstructionsFormData) => {
    setError("");
    setFieldErrors({});

    startTransition(async () => {
      try {
        const result = await sendResetPasswordInstructions(data);

        if (!result.success) {
          // Handle validation errors
          if (result.errors) {
            setFieldErrors(result.errors);
            // Set the first error as the main error message
            const firstError = Object.values(result.errors)[0]?.[0];
            if (firstError) {
              setError(firstError);
            }
          }
          if (result.message) {
            setError(result.message);
          }
        } else {
          // Success - show confirmation message
          setIsSubmitted(true);
        }
      } catch (error) {
        console.error("Send reset password instructions error:", error);
        setError("An unexpected error occurred. Please try again.");
      }
    });
  };

  // Clear errors when user starts typing
  const clearErrorsOnChange = () => {
    if (error) {
      setError("");
    }
    if (Object.keys(fieldErrors).length > 0) {
      setFieldErrors({});
    }
  };

  // If the reset link was successfully sent, show a success message
  if (isSubmitted) {
    return (
      <div>
        <div className="flex flex-col space-y-2 p-6 text-center">
          <h1 className="text-3xl pb-4">Check your email</h1>
          <p className="">
            If you have an account, you will receive an email with instructions
            on how to reset your password in a few minutes.
          </p>
        </div>
        <div className="p-6 pt-0 flex flex-col gap-4 text-center">
          <Link href="/sign-in">
            <Button
              variant="link"
              className="w-full"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to sign in
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <main>
      <Card className="w-full max-w-lg p-6 space-y-6">
        <div>
          <h1 className="text-3xl pb-4">Reset your password</h1>
          <p className="text-muted-foreground">
            Enter your email address and we will send you instructions to reset
            your password.
          </p>
        </div>

        <FormProvider {...methods}>
          <form
            onSubmit={methods.handleSubmit(onSubmit)}
            className="space-y-4"
          >
            {error && <p className="text-sm text-destructive">{error}</p>}
            <FormField
              control={methods.control}
              name="email"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Email</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      type="email"
                      placeholder="<EMAIL>"
                      maxLength={255}
                      autoCapitalize="off"
                      autoComplete="username"
                      disabled={isPending}
                      onChange={(e) => {
                        field.onChange(e);
                        clearErrorsOnChange();
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <Button
              type="submit"
              disabled={isPending}
              size="lg"
              className="w-full"
              loading={isPending}
            >
              {isPending ? "Sending..." : "Send reset instructions"}
            </Button>
          </form>
        </FormProvider>

        <div className="items-center text-sm pt-0 text-muted-foreground flex justify-start gap-1">
          <Link
            className="hover:underline"
            href="/sign-in"
          >
            <ArrowLeft className="h-4 w-4 mr-2 inline" />
            Back to sign in
          </Link>
        </div>
      </Card>
    </main>
  );
};

export default ResetPasswordForm;
