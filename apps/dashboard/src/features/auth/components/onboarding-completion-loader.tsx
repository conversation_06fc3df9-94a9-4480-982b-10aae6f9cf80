"use client";

import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { Spinner } from "@repo/ui/components/spinner";
import { AnimatePresence, motion } from "motion/react";
import { useActiveOrganization } from "@/src/hooks/use-active-organization";

const OnboardingCompletionLoader: React.FC = () => {
  const [currentStage, setCurrentStage] = useState<"creating" | "ready">(
    "creating"
  );
  const router = useRouter();
  const { activeOrganization } = useActiveOrganization();

  useEffect(() => {
    // First stage: "Creating account" for 1.5 seconds
    const firstStageTimer = setTimeout(() => {
      setCurrentStage("ready");
    }, 1500);

    // Second stage: "Getting ready..." for another 1.5 seconds, then redirect
    const redirectTimer = setTimeout(() => {
      if (activeOrganization?.slug) {
        // Redirect to organization home if active organization exists
        router.push(`/${activeOrganization.slug}/home`);
      } else {
        // Fall back to root if no organization
        router.push("/");
      }
    }, 3000);

    return () => {
      clearTimeout(firstStageTimer);
      clearTimeout(redirectTimer);
    };
  }, [router, activeOrganization]);

  return (
    <div className="flex items-center space-x-3">
      <Spinner
        size="small"
        color="primary"
      />

      <div className="w-32">
        <AnimatePresence mode="wait">
          <motion.span
            key={currentStage}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.3 }}
            className="left-0 whitespace-nowrap"
          >
            {currentStage === "creating"
              ? "Creating account"
              : "Getting ready..."}
          </motion.span>
        </AnimatePresence>
      </div>
    </div>
  );
};

export default OnboardingCompletionLoader;
