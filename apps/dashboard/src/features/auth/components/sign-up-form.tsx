"use client";

import { useEffect, useState, useTransition } from "react";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { signUpWithData } from "@/src/features/auth/actions/sign-up";
import {
  signUpSchema,
  type SignUpFormData
} from "@/src/features/auth/schemas/sign-up-schema";
import { validateInvitation } from "@/src/lib/api/invitations";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@repo/ui/components/button";
import { Card } from "@repo/ui/components/card";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormProvider
} from "@repo/ui/components/form";
import { Input } from "@repo/ui/components/input";
import { InputPassword } from "@repo/ui/components/input-password";
import { useForm } from "react-hook-form";

interface InvitationInfo {
  email: string;
  organizationName: string;
  inviterName: string;
}

const SignUpForm = (): React.JSX.Element => {
  const [error, setError] = useState<string>("");
  const [fieldErrors, setFieldErrors] = useState<Record<string, string[]>>({});
  const [invitationInfo, setInvitationInfo] = useState<InvitationInfo | null>(
    null
  );
  const [isPending, startTransition] = useTransition();
  const router = useRouter();
  const searchParams = useSearchParams();

  // Check for pending invitation on mount
  useEffect(() => {
    const loadInvitationData = async () => {
      // Check URL params first for invitation ID
      const invitationId = searchParams.get("invitation");

      // Then check session storage
      const pendingInvitationId = sessionStorage.getItem("pendingInvitationId");
      const pendingInvitationEmail = sessionStorage.getItem(
        "pendingInvitationEmail"
      );

      const activeInvitationId = invitationId || pendingInvitationId;

      if (activeInvitationId) {
        try {
          // Fetch full invitation details including org name and inviter name
          const validationResult = await validateInvitation(activeInvitationId);

          if (
            validationResult.valid &&
            validationResult.email &&
            validationResult.organizationName
          ) {
            setInvitationInfo({
              email: validationResult.email,
              organizationName: validationResult.organizationName,
              inviterName: validationResult.inviterName || "Unknown"
            });

            // Update session storage with the latest data
            sessionStorage.setItem("pendingInvitationId", activeInvitationId);
            sessionStorage.setItem(
              "pendingInvitationEmail",
              validationResult.email
            );
          }
        } catch (error) {
          console.error("Error fetching invitation details:", error);
          // Fallback to just email if we have it
          if (pendingInvitationEmail) {
            setInvitationInfo({
              email: pendingInvitationEmail,
              organizationName: "Unknown", // Fallback
              inviterName: "Unknown"
            });
          }
        }
      } else if (pendingInvitationEmail) {
        // Fallback to session storage email only
        setInvitationInfo({
          email: pendingInvitationEmail,
          organizationName: "Unknown", // Fallback
          inviterName: "Unknown"
        });
      }
    };

    loadInvitationData();
  }, [searchParams]);

  // Initialize form with React Hook Form and Zod validation
  const methods = useForm<SignUpFormData>({
    resolver: zodResolver(signUpSchema),
    defaultValues: {
      firstName: "",
      lastName: "",
      email: "",
      password: ""
    },
    mode: "onSubmit"
  });

  // Update email field when invitation info is loaded
  useEffect(() => {
    if (invitationInfo?.email) {
      methods.setValue("email", invitationInfo.email);
    }
  }, [invitationInfo, methods]);

  // Form submit handler using Auth.js v5 server action
  const onSubmit = async (data: SignUpFormData) => {
    setError("");
    setFieldErrors({});

    startTransition(async () => {
      try {
        const result = await signUpWithData({
          firstName: data.firstName,
          lastName: data.lastName,
          email: data.email,
          password: data.password
        });

        if (!result.success) {
          // Handle validation errors
          if (result.errors) {
            setFieldErrors(result.errors);
            // Set the first error as the main error message
            const firstError = Object.values(result.errors)[0]?.[0];
            if (firstError) {
              setError(firstError);
            }
          }
          if (result.message) {
            setError(result.message);
          }
        } else {
          // Success - redirect to verification page
          router.push(result.redirectTo);
        }
      } catch (error) {
        console.error("Sign up error:", error);
        setError("An unexpected error occurred. Please try again.");
      }
    });
  };

  // Clear errors when user starts typing
  const clearErrorsOnChange = () => {
    if (error) {
      setError("");
    }
    if (Object.keys(fieldErrors).length > 0) {
      setFieldErrors({});
    }
  };

  return (
    <main>
      <Card className="w-full max-w-lg p-6 space-y-6">
        <div>
          <h1 className="text-3xl pb-4">Create your Centaly account</h1>
          {invitationInfo && (
            <p className="text-muted-foreground">
              You&apos;ve been invited to join the{" "}
              <span className="text-primary">
                {invitationInfo.organizationName}
              </span>{" "}
              workspace in Centaly by{" "}
              <span className="text-primary">
                {invitationInfo.inviterName}.
              </span>
            </p>
          )}
        </div>

        <FormProvider {...methods}>
          <form
            onSubmit={methods.handleSubmit(onSubmit)}
            className="space-y-4"
          >
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={methods.control}
                name="firstName"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>First Name</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        type="text"
                        placeholder="First name"
                        maxLength={50}
                        autoComplete="given-name"
                        disabled={isPending}
                        onChange={(e) => {
                          field.onChange(e);
                          clearErrorsOnChange();
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={methods.control}
                name="lastName"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Last Name</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        type="text"
                        placeholder="Last name"
                        maxLength={50}
                        autoComplete="family-name"
                        disabled={isPending}
                        onChange={(e) => {
                          field.onChange(e);
                          clearErrorsOnChange();
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <FormField
              control={methods.control}
              name="email"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Email</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      type="email"
                      placeholder="Enter your email"
                      maxLength={255}
                      autoCapitalize="off"
                      autoComplete="username"
                      disabled={isPending || !!invitationInfo?.email}
                      onChange={(e) => {
                        field.onChange(e);
                        clearErrorsOnChange();
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={methods.control}
              name="password"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Password</FormLabel>
                  <FormControl>
                    <InputPassword
                      {...field}
                      maxLength={72}
                      autoComplete="new-password"
                      disabled={isPending}
                      onChange={(e) => {
                        field.onChange(e);
                        clearErrorsOnChange();
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            {error && (
              <>
                <p className="text-red-500 text-xs">{error}</p>
              </>
            )}
            <div className="text-[13px] text-neutral-500">
              By clicking continue, you acknowledge that you have read and agree
              to Centalys Terms of Service and Privacy Policy.
            </div>
            <Button
              type="submit"
              size="lg"
              className="w-full relative"
              loading={isPending}
              disabled={isPending}
            >
              {isPending ? "Creating account..." : "Get started"}
            </Button>
          </form>
        </FormProvider>
        <p className="text-sm">
          Already have an account?{" "}
          <Link
            href="/sign-in"
            className="underline"
          >
            Sign in
          </Link>
        </p>
      </Card>
    </main>
  );
};

export default SignUpForm;
