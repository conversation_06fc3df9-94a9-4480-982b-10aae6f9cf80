"use client";

import { useState, useTransition } from "react";
import { Button } from "@repo/ui/components/button";

import { signOut } from "../actions/sign-out";

interface SignOutButtonProps {
  variant?:
    | "link"
    | "default"
    | "destructive"
    | "outline"
    | "secondary"
    | "ghost";
  size?: "default" | "sm" | "lg" | "icon";
  className?: string;
  children?: React.ReactNode;
}

export default function SignOutButton({
  variant = "link",
  size = "default",
  className,
  children = "Sign out"
}: SignOutButtonProps) {
  const [isPending, startTransition] = useTransition();
  const [error, setError] = useState<string>("");

  const handleSignOut = async () => {
    setError("");
    
    startTransition(async () => {
      try {
        await signOut();
      } catch (error) {
        console.error("Sign out error:", error);
        setError("Failed to sign out. Please try again.");
      }
    });
  };

  return (
    <>
      <Button
        variant={variant}
        size={size}
        className={className}
        onClick={handleSignOut}
        disabled={isPending}
        loading={isPending}
      >
        {isPending ? "Signing out..." : children}
      </Button>
      {error && (
        <p className="text-red-500 text-xs mt-1">{error}</p>
      )}
    </>
  );
}
