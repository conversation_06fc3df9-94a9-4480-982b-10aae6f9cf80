/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import React, { useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import {
  checkSlugAvailability,
  createWorkspace,
  updateDefaultWorkspace,
  updateOnboardingStatus
} from "@/src/lib/api/onboarding";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@repo/ui/components/button";
import { Card } from "@repo/ui/components/card";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from "@repo/ui/components/form";
import { Input } from "@repo/ui/components/input";
import { FormProvider, useForm } from "react-hook-form";
import { z } from "zod";

// Simplified schema without async validation
const workspaceOnboardingSchema = z.object({
  name: z.string().min(1, "Workspace name is required"),
  slug: z
    .string()
    .min(3, "Slug must be at least 3 characters")
    .max(48, "Slug must be no more than 48 characters")
    .regex(
      /^[a-z0-9]+(?:-[a-z0-9]+)*$/,
      "Slug can only contain lowercase letters, numbers, and hyphens"
    )
});

export type WorkspaceOnboardingFormData = z.infer<
  typeof workspaceOnboardingSchema
>;

const WorkspaceOnboardingForm = () => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string>("");
  const router = useRouter();

  const methods = useForm<WorkspaceOnboardingFormData>({
    resolver: zodResolver(workspaceOnboardingSchema),
    defaultValues: {
      name: "",
      slug: ""
    },
    mode: "onSubmit"
  });

  const onSubmit = async (data: WorkspaceOnboardingFormData) => {
    setIsLoading(true);
    setError("");

    try {
      // First, check if the slug is available
      const slugCheckResult = await checkSlugAvailability(data.slug);

      // If slug is not available, show error
      if (!slugCheckResult.status) {
        setError(
          "This workspace URL is already taken. Please choose a different one."
        );
        setIsLoading(false);
        return;
      }

      // Create organization
      await createWorkspace(data.name, data.slug);

      // Clear form state
      methods.reset();
      setError("");

      // Batch all the post-creation operations to minimize API calls
      try {
        // Perform all operations in parallel where possible
        await Promise.all([
          // Update onboarding status to 'invite'
          updateOnboardingStatus("invite"),
          // Set as default workspace
          updateDefaultWorkspace(data.slug)
        ]);

        // Redirect to invite onboarding page
        router.push("/onboarding/invite");
      } catch (postCreationError) {
        console.error("Error in post-creation operations:", postCreationError);
        // Even if some post-creation operations fail, the organization was created
        // so we should still redirect to the next step
        router.push("/onboarding/invite");
      }
    } catch (err: any) {
      setIsLoading(false);

      // Extract error information
      const message = err?.message || "";
      let errorMessage = "An unexpected error occurred. Please try again.";

      // Handle specific error cases with user-friendly messages
      if (message.includes("401") || message.includes("Unauthorized")) {
        errorMessage =
          "You're not authorized to create workspaces. Please sign in again.";
      } else if (
        message.includes("409") ||
        message.includes("already exists")
      ) {
        errorMessage =
          "This workspace URL is already taken. Please choose a different one.";
      } else if (message.includes("slug")) {
        errorMessage =
          "This workspace URL is already taken. Please choose a different one.";
      } else if (message.includes("name") && message.includes("required")) {
        errorMessage = "Workspace name is required.";
      } else if (message.includes("name") && message.includes("long")) {
        errorMessage = "Workspace name is too long. Please use a shorter name.";
      } else if (message.includes("permission")) {
        errorMessage =
          "You don't have permission to create workspaces. Please contact your administrator.";
      } else if (message.includes("plan") || message.includes("subscription")) {
        errorMessage =
          "Your current plan doesn't allow creating workspaces. Please upgrade your plan.";
      } else if (message.includes("limit")) {
        errorMessage =
          "You've reached the maximum number of workspaces allowed. Please contact support to increase your limit.";
      } else if (message.includes("429") || message.includes("rate")) {
        errorMessage = "Too many requests. Please wait a moment and try again.";
      } else if (message.includes("500") || message.includes("server")) {
        errorMessage =
          "We're experiencing technical difficulties. Please try again in a few minutes.";
      } else if (
        message.includes("network") ||
        message.includes("connection")
      ) {
        errorMessage =
          "Unable to connect to the server. Please check your internet connection and try again.";
      } else if (message) {
        errorMessage = message;
      }

      setError(errorMessage);
    }
  };

  // Clear errors when user starts typing
  const clearErrorsOnChange = () => {
    if (error) {
      setError("");
    }
  };

  return (
    <main>
      <Card className="w-full max-w-lg p-6 space-y-6">
        <div>
          <h1 className="text-3xl pb-4">Create your workspace</h1>
          <p className="text-muted-foreground">
            Create a workspace to build your knowledge base and collaborate with
            your team.{" "}
            <Link
              href="#"
              className="underline hover:text-primary-violet"
            >
              Learn more
            </Link>
          </p>
        </div>
        <FormProvider {...methods}>
          <form
            onSubmit={methods.handleSubmit(onSubmit)}
            className="space-y-4"
          >
            <FormField
              control={methods.control}
              name="name"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Workspace name</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      type="text"
                      placeholder="Enter your workspace name"
                      maxLength={255}
                      autoCapitalize="off"
                      disabled={isLoading}
                      onChange={(e) => {
                        field.onChange(e);
                        clearErrorsOnChange();
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={methods.control}
              name="slug"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Workspace URL</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      id="slug"
                      type="text"
                      required
                      placeholder="acme"
                      minLength={3}
                      maxLength={48}
                      autoCapitalize="off"
                      disabled={isLoading}
                      onChange={(e) => {
                        field.onChange(e);
                        clearErrorsOnChange();
                      }}
                    />
                  </FormControl>
                  <p className="text-sm text-muted-foreground">
                    This will be your workspace URL: app.centaly.com/
                    {field.value || "workspace"}
                  </p>
                  <FormMessage />
                </FormItem>
              )}
            />

            {error && (
              <>
                <p className="text-red-500 text-sm">{error}</p>
              </>
            )}

            <Button
              type="submit"
              size="lg"
              className="w-full relative"
              loading={isLoading}
              onClick={methods.handleSubmit(onSubmit)}
            >
              Create workspace
            </Button>
          </form>
        </FormProvider>
      </Card>
    </main>
  );
};

export default WorkspaceOnboardingForm;
