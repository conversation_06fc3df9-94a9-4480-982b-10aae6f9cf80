import { redirect } from "next/navigation";
import { AuthDAL } from "@/src/features/auth/dal/auth-dal";

interface InviteOnboardingGuardProps {
  children: React.ReactNode;
}

/**
 * Route Protection Component for Invite Onboarding Page
 * 
 * This component implements the Data Access Layer (DAL) pattern for route-level
 * authorization as recommended in the Next.js documentation.
 * 
 * It protects the invite onboarding page by:
 * 1. Checking if the user is authenticated and email verified
 * 2. If not authenticated, redirecting to sign-in
 * 3. If email not verified, redirecting to verification
 * 4. If user has no workspace, redirecting to workspace creation
 * 5. If user has completed onboarding, redirecting to home
 * 6. If all checks pass, allows access to invite step
 * 
 * Access Requirements:
 * - User must be authenticated AND email verified AND have a workspace
 * 
 * Redirect Logic:
 * - If user has no workspace → redirect to /onboarding/workspace
 * - If user has completed onboarding → redirect to home page
 * 
 * Usage:
 * ```tsx
 * export default async function InviteOnboardingPage() {
 *   return (
 *     <InviteOnboardingGuard>
 *       <AuthContainer maxWidth="md">
 *         <InviteOnboardingForm />
 *       </AuthContainer>
 *     </InviteOnboardingGuard>
 *   );
 * }
 * ```
 */
export async function InviteOnboardingGuard({ 
  children 
}: InviteOnboardingGuardProps) {
  // Use the DAL to check if user should be redirected
  const authResult = await AuthDAL.checkInviteOnboardingAccess();

  // If user should be redirected, perform the redirect
  if (authResult.shouldRedirect && authResult.redirectTo) {
    redirect(authResult.redirectTo);
  }

  // If user has proper access, render the invite onboarding page
  return <>{children}</>;
}
