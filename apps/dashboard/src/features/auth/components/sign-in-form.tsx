/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { useState, useTransition } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { signInWithData } from "@/src/features/auth/actions/sign-in";
import {
  signInSchema,
  type SignInFormData
} from "@/src/features/auth/schemas/sign-in-schema";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@repo/ui/components/button";
import { Card } from "@repo/ui/components/card";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormProvider
} from "@repo/ui/components/form";
import { Input } from "@repo/ui/components/input";
import { InputPassword } from "@repo/ui/components/input-password";
import { useForm } from "react-hook-form";

const SignInForm = (): React.JSX.Element => {
  const [error, setError] = useState<string>("");
  const [fieldErrors, setFieldErrors] = useState<Record<string, string[]>>({});
  const [isPending, startTransition] = useTransition();
  const router = useRouter();

  // Initialize form with React Hook Form and Zod validation
  const methods = useForm<SignInFormData>({
    resolver: zodResolver(signInSchema),
    defaultValues: {
      email: "",
      password: ""
    },
    mode: "onSubmit"
  });

  // Form submit handler using server action
  const onSubmit = async (data: SignInFormData) => {
    setError("");
    setFieldErrors({});

    startTransition(async () => {
      try {
        const result = await signInWithData({
          email: data.email,
          password: data.password
        });

        if (!result.success) {
          // Handle validation errors
          if (result.errors) {
            setFieldErrors(result.errors);
            // Set the first error as the main error message
            const firstError = Object.values(result.errors)[0]?.[0];
            if (firstError) {
              setError(firstError);
            }
          }
          if (result.message) {
            setError(result.message);
          }
        } else {
          // Success - redirect to next page
          router.push(result.redirectTo);
        }
      } catch (error) {
        console.error("Sign in error:", error);
        setError("An unexpected error occurred. Please try again.");
      }
    });
  };

  // Clear errors when user starts typing
  const clearErrorsOnChange = () => {
    if (error) {
      setError("");
    }
    if (Object.keys(fieldErrors).length > 0) {
      setFieldErrors({});
    }
  };

  return (
    <main>
      <Card className="w-full max-w-lg p-6 space-y-6">
        <h1 className="text-3xl pb-4">Sign in to Centaly</h1>

        <FormProvider {...methods}>
          <form
            onSubmit={methods.handleSubmit(onSubmit)}
            className="space-y-4"
          >
            <FormField
              control={methods.control}
              name="email"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Email</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      type="email"
                      placeholder="Enter your email"
                      maxLength={255}
                      autoCapitalize="off"
                      autoComplete="username"
                      disabled={isPending}
                      onChange={(e) => {
                        field.onChange(e);
                        clearErrorsOnChange();
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={methods.control}
              name="password"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Password</FormLabel>
                  <FormControl>
                    <InputPassword
                      {...field}
                      maxLength={72}
                      autoComplete="current-password"
                      disabled={isPending}
                      onChange={(e: any) => {
                        field.onChange(e);
                        clearErrorsOnChange();
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <div className="flex gap-2 justify-between items-center">
              {error && (
                <>
                  <p className="text-red-500 text-xs">{error}</p>
                </>
              )}
              <p className="text-xs text-muted-foreground ml-auto">
                <Link
                  href="/reset-password"
                  className="hover:underline cursor-pointer"
                >
                  Forgot your password?
                </Link>
              </p>
            </div>

            <Button
              type="submit"
              size="lg"
              className="w-full relative"
              loading={isPending}
              disabled={isPending}
              onClick={methods.handleSubmit(onSubmit)}
            >
              {isPending ? "Signing in..." : "Sign in"}
            </Button>
          </form>
        </FormProvider>
        <p className="text-sm">
          Don&apos;t have an account?{" "}
          <Link
            href="/sign-up"
            className="underline"
          >
            Sign up
          </Link>
        </p>
      </Card>
    </main>
  );
};

export default SignInForm;
