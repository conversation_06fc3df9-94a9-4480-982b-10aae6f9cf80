import { cache } from "react";
import { headers } from "next/headers";
import { redirect } from "next/navigation";

import { AuthDAL, type OrganizationDTO } from "./auth-dal";

/**
 * Data Transfer Objects (DTOs) for workspace validation
 */
export interface WorkspaceValidationResult {
  isValid: boolean;
  shouldRedirect: boolean;
  redirectTo?: string;
  reason?: string;
  workspace?: OrganizationDTO;
}

/**
 * Data Access Layer for Workspace Authorization
 * Centralizes workspace access validation and authorization logic
 */
export class WorkspaceDAL {
  /**
   * Get organization by slug with user access validation
   * Uses React cache to prevent duplicate API calls within the same request
   */
  static getOrganizationBySlug = cache(
    async (slug: string): Promise<OrganizationDTO | null> => {
      try {
        const headersList = await headers();
        const url = `${process.env.NEXT_PUBLIC_API_URL || "http://localhost:3001"}/api/v1/organizations/${slug}`;

        const response = await fetch(url, {
          headers: {
            Cookie: headersList.get("cookie") || "",
            "Content-Type": "application/json"
          },
          cache: "no-store",
          credentials: "include"
        });

        if (!response.ok) {
          if (response.status === 404) {
            console.debug(`Organization not found: ${slug}`);
            return null;
          }
          if (response.status === 403) {
            console.debug(
              `User not authorized to access organization: ${slug}`
            );
            return null;
          }
          if (response.status === 401) {
            console.debug(
              `User not authenticated to access organization: ${slug}`
            );
            return null;
          }

          console.warn(
            `Failed to fetch organization ${slug}: ${response.status}`
          );
          return null;
        }

        const orgData = await response.json();

        return {
          id: orgData.id,
          name: orgData.name,
          slug: orgData.slug,
          userRole: orgData.userRole
        };
      } catch (error) {
        console.error(`Error fetching organization ${slug}:`, error);
        return null;
      }
    }
  );

  /**
   * Validate workspace access for the current user
   * This method performs the validation and redirects if necessary
   * It should be called from server components that need workspace protection
   */
  static async validateWorkspaceAccess(slug: string): Promise<void> {
    const validationResult = await this.checkWorkspaceAccess(slug);

    if (validationResult.shouldRedirect && validationResult.redirectTo) {
      console.debug(
        `Redirecting from workspace ${slug}: ${validationResult.reason}`
      );
      redirect(validationResult.redirectTo);
    }
  }

  /**
   * Check workspace access without redirecting
   * Returns validation result for programmatic use
   */
  static async checkWorkspaceAccess(
    slug: string
  ): Promise<WorkspaceValidationResult> {
    // First check if user is authenticated
    const sessionData = await AuthDAL.getSessionData();

    if (!sessionData) {
      return {
        isValid: false,
        shouldRedirect: true,
        redirectTo: "/sign-in",
        reason: "User must be authenticated to access workspace"
      };
    }

    const { user } = sessionData;

    // Check if email is verified
    if (!user.emailVerified) {
      return {
        isValid: false,
        shouldRedirect: true,
        redirectTo: "/verify",
        reason: "Email verification required to access workspace"
      };
    }

    // Check if user has completed onboarding
    if (user.onboardingStatus !== "complete") {
      const onboardingRedirect = this.getOnboardingRedirect(
        user.onboardingStatus
      );
      if (onboardingRedirect) {
        return {
          isValid: false,
          shouldRedirect: true,
          redirectTo: onboardingRedirect,
          reason: `User needs to complete onboarding (status: ${user.onboardingStatus})`
        };
      }
    }

    // Try to get the organization by slug
    const workspace = await this.getOrganizationBySlug(slug);

    if (!workspace) {
      // Organization doesn't exist or user doesn't have access
      // Redirect to user's default workspace or first available workspace
      const redirectTo = await this.determineWorkspaceRedirect(user);

      return {
        isValid: false,
        shouldRedirect: true,
        redirectTo,
        reason: `User does not have access to workspace: ${slug}`
      };
    }

    // User has valid access to the workspace
    return {
      isValid: true,
      shouldRedirect: false,
      workspace
    };
  }

  /**
   * Determine the appropriate onboarding redirect based on user's onboarding status
   * Returns null if user has completed onboarding
   */
  private static getOnboardingRedirect(
    onboardingStatus: "incomplete" | "workspace" | "invite" | "complete"
  ): string | null {
    switch (onboardingStatus) {
      case "incomplete":
        return "/onboarding/workspace";
      case "workspace":
        return "/onboarding/invite";
      case "invite":
        return "/onboarding/invite";
      case "complete":
        return null;
      default:
        console.warn(
          `Unexpected onboarding status: ${onboardingStatus}, redirecting to workspace`
        );
        return "/onboarding/workspace";
    }
  }

  /**
   * Determine where to redirect user when they don't have access to requested workspace
   * Implements fallback logic to find an appropriate workspace
   */
  private static async determineWorkspaceRedirect(user: {
    defaultWorkspace: string | null;
    id: string;
  }): Promise<string> {
    // Priority 1: If user has a defaultWorkspace, redirect to it
    if (user.defaultWorkspace) {
      // Validate that user still has access to their default workspace
      const defaultWorkspace = await this.getOrganizationBySlug(
        user.defaultWorkspace
      );
      if (defaultWorkspace) {
        return `/${user.defaultWorkspace}/home`;
      }
    }

    // Priority 2: Get user's organizations and redirect to the first one
    try {
      const sessionDataWithOrgs =
        await AuthDAL.getSessionDataWithOrganizations();
      if (sessionDataWithOrgs && sessionDataWithOrgs.organizations.length > 0) {
        // Use the oldest organization (first in the array as they're ordered by createdAt asc)
        return `/${sessionDataWithOrgs.organizations[0]?.slug}/home`;
      }
    } catch (error) {
      console.warn(
        "Failed to fetch user organizations for workspace redirect:",
        error
      );
    }

    // Priority 3: If user has no organizations, redirect to onboarding
    return "/onboarding/workspace";
  }

  /**
   * Get workspace data for a validated slug
   * This method assumes the workspace access has already been validated
   * Use this when you need workspace data in components after validation
   */
  static async getValidatedWorkspace(
    slug: string
  ): Promise<OrganizationDTO | null> {
    return await this.getOrganizationBySlug(slug);
  }

  /**
   * Utility method to check if user has access to a specific workspace
   * Returns boolean without redirecting
   */
  static async hasWorkspaceAccess(slug: string): Promise<boolean> {
    const result = await this.checkWorkspaceAccess(slug);
    return result.isValid;
  }

  /**
   * Utility method to get user's accessible workspaces
   * Returns list of workspaces the user can access
   */
  static async getUserWorkspaces(): Promise<OrganizationDTO[]> {
    try {
      const sessionData = await AuthDAL.getSessionDataWithOrganizations();
      return sessionData?.organizations || [];
    } catch (error) {
      console.error("Error fetching user workspaces:", error);
      return [];
    }
  }
}
