"use client";

import { useQuery } from "@tanstack/react-query";
import { apiGet } from "@/src/lib/config/api";

interface CurrentUser {
  id: string;
  email: string;
  name: string;
  emailVerified: string | null;
  onboardingStatus: "incomplete" | "workspace" | "invite" | "complete";
  defaultWorkspace: string | null;
  role: string;
}

interface SessionResponse {
  user: CurrentUser | null;
  expires?: string;
}

async function fetchCurrentUser(): Promise<CurrentUser | null> {
  const response = await apiGet("/api/auth/session");

  if (!response.ok) {
    if (response.status === 401) {
      // User is not authenticated
      return null;
    }
    throw new Error(`Failed to fetch current user: ${response.status}`);
  }

  const data: SessionResponse = await response.json();
  return data.user;
}

export function useCurrentUser() {
  return useQuery({
    queryKey: ["currentUser"],
    queryFn: fetchCurrentUser,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: (failureCount, error) => {
      // Don't retry on authentication errors
      if (error && typeof error === "object" && "status" in error) {
        const status = (error as { status: number }).status;
        if (status === 401 || status === 403) {
          return false;
        }
      }
      return failureCount < 3;
    }
  });
}

export type { CurrentUser };
