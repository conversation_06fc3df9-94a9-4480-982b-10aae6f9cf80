"use server";

import { PASSWORD_RESET_EXPIRY_HOURS } from "@repo/auth/constants";
import { ResetPasswordRequest } from "@repo/database";
import { prisma } from "@repo/database/client";
import { sendPasswordResetEmail } from "@repo/email/senders/send-password-reset-email";
import { addHours } from "date-fns";

import { sendResetPasswordInstructionsSchema } from "../schemas/send-reset-password-instructions-schema";

type SendResetPasswordInstructionsResult =
  | { success: true }
  | { success: false; errors: Record<string, string[]>; message: string };

export async function sendResetPasswordInstructions(data: {
  email: string;
}): Promise<SendResetPasswordInstructionsResult> {
  const validatedData = sendResetPasswordInstructionsSchema.safeParse(data);

  if (!validatedData.success) {
    return {
      success: false,
      errors: validatedData.error.flatten().fieldErrors,
      message: "Invalid form data. Please check your inputs."
    };
  }

  const { email } = validatedData.data;
  const normalizedEmail = email.toLowerCase();

  try {
    const maybeUser = await prisma.user.findUnique({
      where: { email: normalizedEmail },
      select: {
        name: true,
        email: true
      }
    });

    // Don't leak information about whether an email is registered or not
    if (!maybeUser || !maybeUser.email) {
      return {
        success: true
      };
    }

    const now = new Date();
    const maybePreviousRequest = await prisma.resetPasswordRequest.findMany({
      where: {
        email: maybeUser.email,
        expires: { gt: now }
      }
    });

    let passwordRequest: ResetPasswordRequest | undefined = undefined;

    if (maybePreviousRequest && maybePreviousRequest?.length >= 1) {
      passwordRequest = maybePreviousRequest[0];
    } else {
      const expiry = addHours(new Date(), PASSWORD_RESET_EXPIRY_HOURS);
      const createdResetPasswordRequest =
        await prisma.resetPasswordRequest.create({
          data: {
            email: maybeUser.email,
            expires: expiry
          }
        });
      passwordRequest = createdResetPasswordRequest;
    }

    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000";
    
    await sendPasswordResetEmail({
      recipient: maybeUser.email,
      name: maybeUser.name,
      resetLink: `${baseUrl}/reset-password/new/${passwordRequest!.id}`
    });

    return {
      success: true
    };
  } catch (error) {
    console.error("Send reset password instructions error:", error);
    return {
      success: false,
      errors: {},
      message: "An unexpected error occurred. Please try again."
    };
  }
}
