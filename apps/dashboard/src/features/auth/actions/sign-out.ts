"use server";

import { cookies } from "next/headers";
import { redirect } from "next/navigation";
import { prisma } from "@repo/database/client";

/**
 * Server action to sign out the user
 * Deletes the session from database and clears the session cookie
 */
export async function signOut() {
  try {
    const cookieStore = await cookies();
    const sessionCookie = cookieStore.get("next-auth.session-token");

    if (sessionCookie) {
      // Delete the session from the database
      await prisma.session.deleteMany({
        where: { sessionToken: sessionCookie.value }
      });

      // Clear the session cookie
      cookieStore.delete("next-auth.session-token");
    }
  } catch (error) {
    console.error("Sign out error:", error);
    // Continue with redirect even if cleanup fails
  }

  // Redirect to sign in page
  redirect("/sign-in");
}