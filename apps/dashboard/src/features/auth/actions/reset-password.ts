"use server";

import { hashPassword } from "@repo/auth/password";
import { prisma } from "@repo/database/client";

import { newPasswordSchema } from "../schemas/new-password-schema";

type ResetPasswordResult =
  | { success: true }
  | { success: false; errors: Record<string, string[]>; message: string };

export async function resetPassword(data: {
  requestId: string;
  password: string;
  confirmPassword: string;
}): Promise<ResetPasswordResult> {
  const validatedData = newPasswordSchema.safeParse({
    password: data.password,
    confirmPassword: data.confirmPassword
  });

  if (!validatedData.success) {
    return {
      success: false,
      errors: validatedData.error.flatten().fieldErrors,
      message: "Invalid form data. Please check your inputs."
    };
  }

  const { password } = validatedData.data;

  try {
    // Find the reset request
    const resetRequest = await prisma.resetPasswordRequest.findUnique({
      where: { id: data.requestId }
    });

    if (!resetRequest) {
      return {
        success: false,
        errors: {},
        message: "Invalid or expired reset link."
      };
    }

    // Check if the request has expired
    if (resetRequest.expires < new Date()) {
      // Clean up expired request
      await prisma.resetPasswordRequest.delete({
        where: { id: data.requestId }
      });

      return {
        success: false,
        errors: {},
        message: "Reset link has expired. Please request a new one."
      };
    }

    // Find the user by email from the reset request
    const user = await prisma.user.findUnique({
      where: { email: resetRequest.email }
    });

    if (!user) {
      return {
        success: false,
        errors: {},
        message: "User not found."
      };
    }

    // Hash the new password
    const hashedPassword = await hashPassword(password);

    // Update the user's password
    await prisma.user.update({
      where: { id: user.id },
      data: {
        password: hashedPassword,
        lastLogin: new Date() // Update last login as password has been reset
      }
    });

    // Delete the reset request (it's been used)
    await prisma.resetPasswordRequest.delete({
      where: { id: data.requestId }
    });

    return {
      success: true
    };
  } catch (error) {
    console.error("Password reset error:", error);
    return {
      success: false,
      errors: {},
      message: "An unexpected error occurred. Please try again."
    };
  }
}