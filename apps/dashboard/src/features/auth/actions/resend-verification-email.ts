"use server";

import { createOtpTokens } from "@repo/auth/verification";
import { prisma } from "@repo/database/client";
import { sendVerifyEmail } from "@repo/email/senders/send-verify-email-address-email";
import { z } from "zod";

const resendEmailSchema = z.object({
  email: z.string().email("Invalid email address")
});

type ResendResult =
  | { success: true; message: string }
  | { success: false; message: string };

export async function resendVerificationEmail(
  data: z.infer<typeof resendEmailSchema>
): Promise<ResendResult> {
  const validatedData = resendEmailSchema.safeParse(data);

  if (!validatedData.success) {
    return {
      success: false,
      message: "Invalid email address"
    };
  }

  const { email } = validatedData.data;
  const normalizedEmail = email.toLowerCase();

  try {
    // Check if user exists and is not already verified
    const user = await prisma.user.findUnique({
      where: { email: normalizedEmail },
      select: {
        id: true,
        email: true,
        emailVerified: true
      }
    });

    if (!user) {
      return {
        success: false,
        message: "No account found with this email address"
      };
    }

    if (user.emailVerified) {
      return {
        success: false,
        message: "Email is already verified"
      };
    }

    // Generate new OTP tokens for email verification
    const { hashedOtp } = await createOtpTokens(normalizedEmail);

    // Get base URL from environment variables
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000";

    // Send verification email
    await sendVerifyEmail({
      recipient: normalizedEmail,
      email: normalizedEmail,
      verifyLink: `${baseUrl}/verify-email/${hashedOtp}`
    });

    console.log(`Verification email resent to ${normalizedEmail}`);

    return {
      success: true,
      message: "Verification email sent successfully"
    };
  } catch (error) {
    console.error("Resend verification email error:", error);

    return {
      success: false,
      message: "Failed to send verification email. Please try again."
    };
  }
}
