"use client";

import React, { useState, useTransition } from "react";
import { signOut } from "@/src/features/auth/actions/sign-out";
import { getFirstLastInitials } from "@/src/lib/utils/get-initials";
import {
  Avatar,
  AvatarFallback,
  AvatarImage
} from "@repo/ui/components/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from "@repo/ui/components/dropdown-menu";
import {
  CreditCard,
  Gift,
  LogOut,
  MessageSquareShare,
  User
} from "lucide-react";

interface UserMenuProps {
  image?: string;
  name: string;
  email: string;
}

const UserMenu = ({ image, name, email }: UserMenuProps) => {
  const [isPending, startTransition] = useTransition();
  const [error, setError] = useState<string>("");

  const handleSignOut = async () => {
    setError("");

    startTransition(async () => {
      try {
        await signOut();
      } catch (error) {
        console.error("Sign out error:", error);
        setError("Failed to sign out. Please try again.");
      }
    });
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Avatar className="h-8 w-8 rounded-full cursor-pointer ml-1">
          <AvatarImage
            src={image ?? undefined}
            alt={name}
          />
          <AvatarFallback className="rounded-full">
            {getFirstLastInitials(name || "")}
          </AvatarFallback>
        </Avatar>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
        side="bottom"
        align="end"
        sideOffset={4}
      >
        <DropdownMenuLabel className="p-0 font-normal">
          <div className="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
            <Avatar className="h-8 w-8 rounded-full">
              <AvatarImage
                src={image ?? undefined}
                alt={name}
              />
              <AvatarFallback className="rounded-full">
                {getFirstLastInitials(name || "")}
              </AvatarFallback>
            </Avatar>
            <div className="grid flex-1 text-left text-sm leading-tight">
              <span className="truncate font-medium">{name}</span>
              <span className="truncate text-xs">{email}</span>
            </div>
          </div>
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          <DropdownMenuItem>
            <User />
            Profile
          </DropdownMenuItem>
          <DropdownMenuItem>
            <CreditCard />
            Billing
          </DropdownMenuItem>
          <DropdownMenuItem>
            <Gift />
            What&apos;s new
          </DropdownMenuItem>
          <DropdownMenuItem>
            <MessageSquareShare />
            Share feedback
          </DropdownMenuItem>
        </DropdownMenuGroup>
        <DropdownMenuSeparator />
        <DropdownMenuItem
          onClick={handleSignOut}
          disabled={isPending}
        >
          <LogOut />
          Log out
        </DropdownMenuItem>
        {error && (
          <div className="px-2 py-1">
            <p className="text-red-500 text-xs">{error}</p>
          </div>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default UserMenu;
