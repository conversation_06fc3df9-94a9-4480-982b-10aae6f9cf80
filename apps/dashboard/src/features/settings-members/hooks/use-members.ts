"use client";

import {
  fetchMembers,
  removeMember,
  resendInvitation,
  transferOwnership,
  updateMember,
  type FetchMembersParams,
  type Member,
  type MembersResponse,
  type UpdateMemberRequest
} from "@/src/lib/api/members";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

// Hook for fetching members with React Query
export function useMembers(
  organizationSlug: string,
  params: FetchMembersParams = {},
  initialData?: MembersResponse
) {
  return useQuery({
    queryKey: ["members", organizationSlug, params],
    queryFn: () => fetchMembers(organizationSlug, params),
    initialData,
    staleTime: 30000, // 30 seconds
    refetchOnWindowFocus: false
  });
}

// Hook for updating members with optimistic updates
export function useUpdateMember(organizationSlug: string) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      memberId,
      data
    }: {
      memberId: string;
      data: UpdateMemberRequest;
    }) => {
      // Handle all updates (role changes and ban/unban) using existing API
      return updateMember(organizationSlug, memberId, data);
    },
    onMutate: async ({ memberId, data }) => {
      // Cancel any outgoing refetches (using partial match to cancel all member queries)
      await queryClient.cancelQueries({
        queryKey: ["members", organizationSlug],
        exact: false
      });

      // Snapshot the previous value from any existing member query
      const queryCache = queryClient.getQueryCache();
      const memberQueries = queryCache.findAll({
        queryKey: ["members", organizationSlug],
        exact: false
      });

      let previousMembers: MembersResponse | undefined;
      for (const query of memberQueries) {
        const data = query.state.data as MembersResponse | undefined;
        if (data?.members) {
          previousMembers = data;
          break;
        }
      }

      // Optimistically update all member queries
      for (const query of memberQueries) {
        if (query.queryKey) {
          queryClient.setQueryData(
            query.queryKey,
            (old: MembersResponse | undefined) => {
              if (!old?.members) return old;

              return {
                ...old,
                members: old.members.map((member: Member) =>
                  member.id === memberId
                    ? {
                        ...member,
                        ...(data.role && { role: data.role }),
                        // Handle ban logic
                        ...(data.ban !== undefined && {
                          status: data.ban ? "suspended" : "active"
                        })
                      }
                    : member
                )
              };
            }
          );
        }
      }

      // Return a context object with the snapshotted value
      return { previousMembers };
    },
    onError: (error: Error, _variables, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousMembers) {
        // Restore all member queries to their previous state
        const queryCache = queryClient.getQueryCache();
        const memberQueries = queryCache.findAll({
          queryKey: ["members", organizationSlug],
          exact: false
        });

        for (const query of memberQueries) {
          if (query.queryKey) {
            queryClient.setQueryData(query.queryKey, context.previousMembers);
          }
        }
      }
      toast.error(error.message || "Failed to update member");
    },
    onSuccess: (_updatedMember, { data }) => {
      let successMessage = "Member updated successfully";
      if (data.role) {
        successMessage = `Member role updated to ${data.role}`;
      } else if (data.ban !== undefined) {
        successMessage = data.ban ? "Member suspended" : "Member reactivated";
      }
      toast.success(successMessage);
    },
    onSettled: () => {
      // Always refetch after error or success
      queryClient.invalidateQueries({
        queryKey: ["members", organizationSlug],
        exact: false
      });
    }
  });
}

// Hook for removing members with optimistic updates
export function useRemoveMember(organizationSlug: string) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (memberId: string) => removeMember(organizationSlug, memberId),
    onMutate: async (memberId) => {
      // Cancel any outgoing refetches (using partial match to cancel all member queries)
      await queryClient.cancelQueries({
        queryKey: ["members", organizationSlug],
        exact: false
      });

      // Snapshot the previous value from any existing member query
      const queryCache = queryClient.getQueryCache();
      const memberQueries = queryCache.findAll({
        queryKey: ["members", organizationSlug],
        exact: false
      });

      let previousMembers: MembersResponse | undefined;
      for (const query of memberQueries) {
        const data = query.state.data as MembersResponse | undefined;
        if (data?.members) {
          previousMembers = data;
          break;
        }
      }

      // Optimistically update all member queries
      for (const query of memberQueries) {
        if (query.queryKey) {
          queryClient.setQueryData(
            query.queryKey,
            (old: MembersResponse | undefined) => {
              if (!old?.members) return old;

              return {
                ...old,
                members: old.members.filter(
                  (member: Member) => member.id !== memberId
                ),
                pagination: {
                  ...old.pagination,
                  total: old.pagination.total - 1
                }
              };
            }
          );
        }
      }

      // Return a context object with the snapshotted value
      return { previousMembers };
    },
    onError: (error: Error, _variables, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousMembers) {
        // Restore all member queries to their previous state
        const queryCache = queryClient.getQueryCache();
        const memberQueries = queryCache.findAll({
          queryKey: ["members", organizationSlug],
          exact: false
        });

        for (const query of memberQueries) {
          if (query.queryKey) {
            queryClient.setQueryData(query.queryKey, context.previousMembers);
          }
        }
      }
      toast.error(error.message || "Failed to remove member");
    },
    onSuccess: (_, memberId) => {
      const isInvitation = memberId.startsWith("invitation-");
      toast.success(
        isInvitation ? "Invitation cancelled" : "Member removed successfully"
      );
    },
    onSettled: () => {
      // Always refetch after error or success
      queryClient.invalidateQueries({
        queryKey: ["members", organizationSlug],
        exact: false
      });
    }
  });
}

// Hook to get member status statistics
export function useMemberStats(organizationSlug: string) {
  const { data } = useMembers(organizationSlug);

  return {
    total: data?.pagination.total || 0,
    active: data?.members.filter((m) => m.status === "active").length || 0,
    pending: data?.members.filter((m) => m.status === "pending").length || 0,
    suspended: data?.members.filter((m) => m.status === "suspended").length || 0
  };
}

// Hook for resending invitations
export function useResendInvitation(organizationSlug: string) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (invitationId: string) => {
      return resendInvitation(organizationSlug, invitationId);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["members", organizationSlug],
        exact: false
      });
      toast.success("Invitation resent successfully");
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to resend invitation");
    }
  });
}

// Hook for transferring organization ownership
export function useTransferOwnership(organizationSlug: string) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (newOwnerId: string) =>
      transferOwnership(organizationSlug, newOwnerId),
    onMutate: async (newOwnerId) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({
        queryKey: ["members", organizationSlug],
        exact: false
      });

      // Snapshot the previous value
      const queryCache = queryClient.getQueryCache();
      const memberQueries = queryCache.findAll({
        queryKey: ["members", organizationSlug],
        exact: false
      });

      let previousMembers: MembersResponse | undefined;
      for (const query of memberQueries) {
        const data = query.state.data as MembersResponse | undefined;
        if (data?.members) {
          previousMembers = data;
          break;
        }
      }

      // Optimistically update all member queries
      for (const query of memberQueries) {
        if (query.queryKey) {
          queryClient.setQueryData(
            query.queryKey,
            (old: MembersResponse | undefined) => {
              if (!old?.members) return old;

              return {
                ...old,
                members: old.members.map((member: Member) => {
                  // Update the new owner
                  if (member.id === newOwnerId) {
                    return { ...member, role: "owner" as const };
                  }
                  // Update current owner to admin
                  if (member.role === "owner") {
                    return { ...member, role: "admin" as const };
                  }
                  return member;
                })
              };
            }
          );
        }
      }

      return { previousMembers };
    },
    onError: (error: Error, _variables, context) => {
      // Rollback on error
      if (context?.previousMembers) {
        const queryCache = queryClient.getQueryCache();
        const memberQueries = queryCache.findAll({
          queryKey: ["members", organizationSlug],
          exact: false
        });

        for (const query of memberQueries) {
          if (query.queryKey) {
            queryClient.setQueryData(query.queryKey, context.previousMembers);
          }
        }
      }
      toast.error(error.message || "Failed to transfer ownership");
    },
    onSuccess: (result) => {
      toast.success(
        `Ownership transferred to ${result.newOwner.user.name || result.newOwner.user.email}`
      );
    },
    onSettled: () => {
      // Always refetch after completion
      queryClient.invalidateQueries({
        queryKey: ["members", organizationSlug],
        exact: false
      });
    }
  });
}

// Convenience hook for member management with all mutations
export function useMemberManagement(organizationSlug: string) {
  return {
    updateMember: useUpdateMember(organizationSlug),
    removeMember: useRemoveMember(organizationSlug),
    resendInvitation: useResendInvitation(organizationSlug),
    transferOwnership: useTransferOwnership(organizationSlug)
  };
}
