"use client";

import React, { useRef, useState } from "react";
import { useParams } from "next/navigation";
import { useZodForm } from "@/src/hooks/use-zod-form";
import { createInvitations } from "@/src/lib/api/members";
import { <PERSON><PERSON> } from "@repo/ui/components/button";
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from "@repo/ui/components/dialog";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from "@repo/ui/components/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@repo/ui/components/select";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { X } from "lucide-react";
import { FormProvider } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";

interface EmailTag {
  id: string;
  email: string;
}

interface TeamMemberInviteModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function TeamMemberInviteModal({
  isOpen,
  onClose
}: TeamMemberInviteModalProps) {
  const [emailTags, setEmailTags] = useState<EmailTag[]>([]);
  const [inputValue, setInputValue] = useState("");
  const inputRef = useRef<HTMLInputElement>(null);
  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email.trim());
  };

  const createTag = (email: string) => {
    const trimmedEmail = email.trim();
    if (!trimmedEmail) return null;
    if (!validateEmail(trimmedEmail)) return null;
    if (emailTags.some((tag) => tag.email === trimmedEmail)) return null;

    return {
      id: crypto.randomUUID(),
      email: trimmedEmail
    };
  };

  const addTags = (emails: string[]) => {
    const newTags = emails
      .map(createTag)
      .filter((tag): tag is EmailTag => tag !== null);

    if (newTags.length > 0) {
      const updatedTags = [...emailTags, ...newTags];
      setEmailTags(updatedTags);
      // Update form field
      methods.setValue(
        "emails",
        updatedTags.map((tag) => tag.email)
      );
      setInputValue("");
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter" || e.key === ",") {
      e.preventDefault();
      if (inputValue) {
        addTags([inputValue]);
      }
    } else if (e.key === "Backspace" && !inputValue && emailTags.length > 0) {
      e.preventDefault();
      removeTag(emailTags[emailTags.length - 1]?.id || "");
    }
  };

  const handlePaste = (e: React.ClipboardEvent) => {
    e.preventDefault();
    const pastedText = e.clipboardData.getData("text");
    const emails = pastedText.split(/[,\n]/).map((email) => email.trim());
    addTags(emails);
  };

  const handleBlur = () => {
    if (inputValue) {
      const newTag = createTag(inputValue);
      if (newTag) {
        setEmailTags((prev) => [...prev, newTag]);
      }
      // Clear the input regardless of validity to prevent stale text.
      setInputValue("");
    }
  };

  const removeTag = (id: string) => {
    const updatedTags = emailTags.filter((tag) => tag.id !== id);
    setEmailTags(updatedTags);
    // Update form field
    methods.setValue(
      "emails",
      updatedTags.map((tag) => tag.email)
    );
  };

  const params = useParams();
  const organizationSlug = params?.slug as string;
  const queryClient = useQueryClient();

  const methods = useZodForm({
    schema: z.object({
      emails: z.array(z.string().email()),
      role: z.enum(["viewer", "contributor", "admin"])
    }),
    mode: "onSubmit",
    defaultValues: {
      emails: [],
      role: "contributor" as const
    }
  });

  // Create invitation mutation using new API
  const inviteMutation = useMutation({
    mutationFn: async (
      invitations: Array<{
        email: string;
        role: "viewer" | "contributor" | "admin";
      }>
    ) => {
      return createInvitations(organizationSlug, invitations);
    },
    onSuccess: () => {
      // Invalidate and refetch members
      queryClient.invalidateQueries({
        queryKey: ["members", organizationSlug]
      });
      toast.success("Invitations sent successfully");
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to send invitations");
    }
  });

  const onSubmit = async (data: {
    emails: string[];
    role: "viewer" | "contributor" | "admin";
  }) => {
    const emailsToInvite = emailTags.map((tag) => tag.email);

    if (emailsToInvite.length === 0) {
      return;
    }

    // Create invitations array
    const invitations = emailsToInvite.map((email) => ({
      email,
      role: data.role
    }));

    try {
      await inviteMutation.mutateAsync(invitations);

      // Reset form on success
      setEmailTags([]);
      methods.reset({
        emails: [],
        role: "contributor"
      });
      // Close modal after successful submission
      onClose();
    } catch (error) {
      console.error("Failed to send invitations:", error);
    }
  };

  return (
    <FormProvider {...methods}>
      <Dialog
        open={isOpen}
        onOpenChange={onClose}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Invite Members</DialogTitle>
          </DialogHeader>
          <form
            onSubmit={methods.handleSubmit(onSubmit)}
            className="space-y-4"
          >
            <FormField
              control={methods.control}
              name="emails"
              render={() => (
                <FormItem className="flex w-full flex-col">
                  <FormLabel>Send Invitation to</FormLabel>
                  <FormControl>
                    <div
                      className="min-h-[120px] w-full rounded-md border border-input bg-transparent p-2 text-sm ring-offset-background focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2"
                      onClick={() => inputRef.current?.focus()}
                    >
                      <div className="flex flex-wrap gap-1.5">
                        {emailTags.map((tag) => (
                          <span
                            key={tag.id}
                            className="inline-flex items-center gap-1 rounded-md bg-secondary px-2 py-1 text-sm"
                            tabIndex={0}
                          >
                            {tag.email}
                            <button
                              type="button"
                              onClick={() => removeTag(tag.id)}
                              className="text-muted-foreground hover:text-foreground"
                            >
                              <X size={14} />
                            </button>
                          </span>
                        ))}
                        <input
                          ref={inputRef}
                          id="email-input"
                          type="text"
                          required
                          className="flex-1 bg-transparent outline-none min-w-[200px] placeholder:text-muted-foreground"
                          placeholder={
                            emailTags.length === 0
                              ? "Type or paste email addresses..."
                              : ""
                          }
                          value={inputValue}
                          onChange={(e) => setInputValue(e.target.value)}
                          onKeyDown={handleKeyDown}
                          onPaste={handlePaste}
                          onBlur={handleBlur}
                          disabled={inviteMutation.isPending}
                        />
                      </div>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={methods.control}
              name="role"
              render={({ field }) => (
                <FormItem className="flex w-full flex-col">
                  <FormLabel>Role</FormLabel>
                  <FormControl>
                    <Select
                      required
                      value={field.value}
                      onValueChange={field.onChange}
                      disabled={inviteMutation.isPending}
                    >
                      <SelectTrigger className="w-full capitalize">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem
                          className="capitalize"
                          value="viewer"
                        >
                          Viewer
                        </SelectItem>
                        <SelectItem
                          className="capitalize"
                          value="contributor"
                        >
                          Contributor
                        </SelectItem>
                        <SelectItem
                          className="capitalize"
                          value="admin"
                        >
                          Admin
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                disabled={inviteMutation.isPending}
              >
                Cancel
              </Button>
              <Button
                type="button"
                variant="default"
                disabled={emailTags.length === 0 || inviteMutation.isPending}
                loading={inviteMutation.isPending}
                onClick={methods.handleSubmit(onSubmit)}
              >
                Send invitation
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </FormProvider>
  );
}
