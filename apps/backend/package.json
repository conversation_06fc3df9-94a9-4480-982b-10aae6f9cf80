{"name": "backend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "lint": "eslint .", "typecheck": "tsc --noEmit"}, "dependencies": {"@auth/core": "^0.40.0", "@aws-sdk/client-s3": "^3.859.0", "@aws-sdk/s3-request-presigner": "^3.859.0", "@hono/node-server": "^1.15.0", "@hono/swagger-ui": "^0.5.2", "@hono/zod-openapi": "^0.19.9", "@repo/auth": "workspace:*", "@repo/database": "workspace:*", "@repo/email": "workspace:*", "dotenv": "^17.1.0", "hono": "^4.8.4", "jsonwebtoken": "^9.0.2", "zod": "^3.25.76"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/prettier-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^22.15.3", "eslint": "^9.25.0", "tsx": "^4.7.1", "typescript": "^5.8.3"}, "prettier": "@repo/prettier-config", "packageManager": "pnpm@9.15.4"}