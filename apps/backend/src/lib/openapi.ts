import { swaggerUI } from "@hono/swagger-ui";
import { createRoute, OpenAPIHono, z } from "@hono/zod-openapi";

export type Variables = {
  requestId: string;
  authUser: {
    token: {
      name?: string | null;
      email?: string | null;
      picture?: string | null;
      sub?: string;
    };
    user?: {
      id?: string;
      name?: string | null;
      email?: string | null;
      image?: string | null;
    };
  };
  // Backward compatibility
  user: {
    id?: string;
    name?: string | null;
    email?: string | null;
    image?: string | null;
  } | null;
  session: {
    user: {
      id?: string;
      name?: string | null;
      email?: string | null;
      image?: string | null;
    };
  } | null;
};

// Create OpenAPI app instance
export const createOpenAPIApp = () => {
  const app = new OpenAPIHono<{ Variables: Variables }>();

  // OpenAPI documentation route
  app.doc("/openapi.json", {
    openapi: "3.0.0",
    info: {
      title: "Centaly API",
      version: "1.0.0",
      description: "API documentation for Centaly application"
    },
    servers: [
      {
        url: "http://localhost:3001",
        description: "Development server"
      },
      {
        url: "https://api.centaly.com",
        description: "Production server"
      }
    ]
  });

  // Swagger UI route
  app.get("/swagger", swaggerUI({ url: "/api/v1/openapi.json" }));

  return app;
};

// Example of creating a typed route
export const exampleRoute = createRoute({
  method: "get",
  path: "/example",
  tags: ["Example"],
  summary: "Example endpoint",
  description: "This is an example endpoint showing OpenAPI documentation",
  request: {
    query: z.object({
      name: z
        .string()
        .optional()
        .openapi({
          param: {
            description: "Name to greet",
            example: "World"
          }
        })
    })
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: z.object({
            message: z.string(),
            timestamp: z.string()
          })
        }
      },
      description: "Successful response"
    },
    400: {
      content: {
        "application/json": {
          schema: z.object({
            error: z.string(),
            details: z.any().optional()
          })
        }
      },
      description: "Bad request"
    }
  }
});

// Helper function to create error response
export const createErrorResponse = (
  status: number,
  message: string,
  details?: any
) => {
  return {
    [status]: {
      content: {
        "application/json": {
          schema: z.object({
            error: z.string(),
            details: z.any().optional(),
            timestamp: z.string()
          })
        }
      },
      description: message
    }
  };
};

// Common response schemas
export const schemas = {
  error: z.object({
    error: z.string(),
    details: z.any().optional(),
    timestamp: z.string()
  }),

  pagination: z.object({
    page: z.number().int().positive(),
    pageSize: z.number().int().positive().max(100),
    total: z.number().int().nonnegative(),
    totalPages: z.number().int().nonnegative()
  }),

  success: z.object({
    success: z.boolean(),
    message: z.string()
  })
};
