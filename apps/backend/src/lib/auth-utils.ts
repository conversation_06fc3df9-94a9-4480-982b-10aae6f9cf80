import crypto from "crypto";
import { sign } from "hono/jwt";
import jwt from "jsonwebtoken";

import { env } from "./env.js";

export interface JWTPayload {
  sub: string;
  email: string;
  name: string;
  iat: number;
  exp: number;
  [key: string]: any;
}

export interface User {
  id: string;
  email: string;
  name: string;
  emailVerified?: Date | null;
  onboardingStatus?: "incomplete" | "workspace" | "invite" | "complete";
  defaultWorkspace?: string | null;
  role?: string;
}

export interface DecodedVerificationToken {
  userId: string;
  email: string;
  purpose: string;
  expires: string;
}

// Encryption functions for token decryption
export function deriveKey(key: string): Buffer {
  return crypto.createHash("sha256").update(key).digest();
}

export function symmetricDecrypt(text: string, key: string): string {
  const components = text.split(":");
  const iv_from_ciphertext = Buffer.from(components.shift() || "", "hex");
  const decipher = crypto.createDecipheriv(
    "aes256",
    deriveKey(key),
    iv_from_ciphertext
  );
  let deciphered = decipher.update(components.join(":"), "hex", "utf8");
  deciphered += decipher.final("utf8");
  return deciphered;
}

// Date validation functions
export function isValid(date: Date): boolean {
  return date instanceof Date && !isNaN(date.getTime());
}

export function isBefore(date1: Date, date2: Date): boolean {
  return date1.getTime() < date2.getTime();
}

// JWT token creation and verification functions
export async function createJWTToken(user: User): Promise<string> {
  const jwtPayload: JWTPayload = {
    sub: user.id,
    email: user.email,
    name: user.name,
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + 60 * 60 * 24 * 30 // 30 days
  };

  return await sign(jwtPayload, env.AUTH_SECRET!);
}

export function verifyJWTToken(token: string): Promise<JWTPayload> {
  return new Promise((resolve, reject) => {
    jwt.verify(
      token,
      env.AUTH_SECRET!,
      (
        err: jwt.VerifyErrors | null,
        decoded: jwt.JwtPayload | string | undefined
      ) => {
        if (err) {
          reject(err);
        } else if (decoded && typeof decoded === "object") {
          resolve({
            sub: (decoded as jwt.JwtPayload).sub || "",
            email: (decoded as any).email || "",
            name: (decoded as any).name || "",
            iat: (decoded as jwt.JwtPayload).iat || 0,
            exp: (decoded as jwt.JwtPayload).exp || 0
          });
        } else {
          reject(new Error("Invalid token payload"));
        }
      }
    );
  });
}

// Token validation functions
export function validateVerificationToken(
  decoded: DecodedVerificationToken
): boolean {
  return (
    !!decoded.userId &&
    !!decoded.email &&
    decoded.purpose === "email-verification" &&
    isValid(new Date(decoded.expires)) &&
    !isBefore(new Date(decoded.expires), new Date())
  );
}

// Session cookie formatting
export function formatSessionCookie(
  sessionToken: string,
  isDevelopment: boolean
): string {
  const maxAge = 60 * 60 * 24 * 30; // 30 days
  return `next-auth.session-token=${sessionToken}; HttpOnly; Secure=${!isDevelopment}; SameSite=Lax; Path=/; Max-Age=${maxAge}`;
}

// Parse session token from cookie header
export function parseSessionTokenFromCookie(
  cookieHeader: string | undefined
): string | null {
  if (!cookieHeader) {
    return null;
  }

  const sessionMatch = cookieHeader.match(/next-auth\.session-token=([^;]+)/);
  return sessionMatch ? sessionMatch[1] || null : null;
}

// Parse token from authorization header
export function parseTokenFromAuthHeader(
  authHeader: string | undefined
): string | null {
  if (!authHeader?.startsWith("Bearer ")) {
    return null;
  }
  return authHeader.substring(7);
}
