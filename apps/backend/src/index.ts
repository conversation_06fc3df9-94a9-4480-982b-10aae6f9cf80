/* eslint-disable @typescript-eslint/no-explicit-any */
import { serve } from "@hono/node-server";
import { Hono } from "hono";
import { cors } from "hono/cors";
import { logger } from "hono/logger";
import { prettyJSON } from "hono/pretty-json";

import { env, isDevelopment } from "./lib/env.js";
import {
  createOpenAPIApp,
  exampleRoute,
  type Variables
} from "./lib/openapi.js";
import { errorHandler } from "./middleware/error-handler.js";
import { requestId } from "./middleware/request-id.js";
import { getSecureHeadersMiddleware } from "./middleware/secure-headers.js";
import { sessionAuth } from "./middleware/session-auth.js";
import { registerRoutes, authRouter } from "./routes/index.js";

const app = new Hono<{ Variables: Variables }>();

// Add middleware - order matters
app.use("*", requestId);
app.use("*", errorHandler);
app.use("*", logger());

app.use(
  "*",
  cors({
    origin: (origin) => {
      const allowedOrigins = [
        "http://localhost:3000", // Dashboard dev server
        "http://localhost:3001", // API dev server (for Swagger UI)
        "https://app.centaly.com", // Production frontend
        "https://staging.centaly.com", // Staging frontend
        "https://api-staging.centaly.com", // Staging API
        "https://api.centaly.com", // Production API
        env.FRONTEND_URL || "http://localhost:3000"
      ].filter(Boolean);

      // Allow any Vercel preview URL
      if (origin && origin.endsWith(".vercel.app")) {
        return origin;
      }

      // Allow configured origins
      return allowedOrigins.includes(origin || "") ? origin : null;
    },

    allowMethods: ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
    allowHeaders: ["Content-Type", "Authorization", "Cookie"],
    exposeHeaders: ["Content-Length", "X-Request-Id", "Set-Cookie"],
    maxAge: 86400,
    credentials: true
  })
);

// Apply secure headers AFTER CORS
app.use("*", getSecureHeadersMiddleware());

// Apply session authentication middleware to protected routes
app.use("/api/v1/*", sessionAuth);

// Only use prettyJSON in development
if (isDevelopment) {
  app.use("*", prettyJSON());
}

// Health check endpoint
app.get("/health", (c) => {
  return c.json({
    status: "ok",
    timestamp: new Date().toISOString(),
    service: "Centaly API"
  });
});

// API routes - using OpenAPIHono for documentation
const api = createOpenAPIApp();

// Example route with OpenAPI documentation
api.openapi(exampleRoute, (c) => {
  const { name = "World" } = c.req.query();
  return c.json(
    {
      message: `Hello, ${name}!`,
      timestamp: new Date().toISOString()
    },
    200
  );
});

// Register all routes
registerRoutes(api as any);

// Mount auth routes directly under /api
app.route("/api", authRouter);

// Mount API routes
app.route("/api/v1", api);

// 404 handler
app.notFound((c) => {
  const requestId = c.get("requestId");
  return c.json(
    {
      error: "Route not found",
      requestId,
      timestamp: new Date().toISOString()
    },
    404
  );
});

// Start the server
const port = env.PORT;

console.log(
  `🚀 Centaly API Server starting on port ${port} in ${env.NODE_ENV} mode`
);

const server = serve({
  fetch: app.fetch,
  port,
  hostname: "0.0.0.0" // Listen on all interfaces for containerized environments
});

// Enhanced graceful shutdown
const gracefulShutdown = (signal: string) => {
  console.log(`🛑 Received ${signal}, shutting down gracefully...`);

  // Set a timeout to force exit if graceful shutdown takes too long
  const forceExitTimeout = setTimeout(() => {
    console.log("⚠️  Forcing exit after timeout");
    process.exit(1);
  }, 5000); // 5 second timeout

  try {
    if (server && typeof server.close === "function") {
      server.close(() => {
        console.log("✅ Server closed successfully");
        clearTimeout(forceExitTimeout);
        process.exit(0);
      });
    } else {
      console.log("⚠️  Server close method not available, forcing exit");
      clearTimeout(forceExitTimeout);
      process.exit(0);
    }
  } catch (error) {
    console.error("❌ Error during shutdown:", error);
    clearTimeout(forceExitTimeout);
    process.exit(1);
  }
};

// Handle various termination signals
process.on("SIGINT", () => gracefulShutdown("SIGINT"));
process.on("SIGTERM", () => gracefulShutdown("SIGTERM"));
process.on("SIGQUIT", () => gracefulShutdown("SIGQUIT"));

// Handle uncaught exceptions and unhandled rejections
process.on("uncaughtException", (error) => {
  console.error("❌ Uncaught Exception:", error);
  gracefulShutdown("uncaughtException");
});

process.on("unhandledRejection", (reason, promise) => {
  console.error("❌ Unhandled Rejection at:", promise, "reason:", reason);
  gracefulShutdown("unhandledRejection");
});

// Handle Windows-specific signals
if (process.platform === "win32") {
  import("readline").then(({ createInterface }) => {
    const rl = createInterface({
      input: process.stdin,
      output: process.stdout
    });

    rl.on("SIGINT", () => {
      process.emit("SIGINT" as any);
    });
  });
}
