import { prisma } from "@repo/database/client";
import type { Context, Next } from "hono";

import { parseSessionTokenFromCookie, type User } from "../lib/auth-utils.js";
import type { Variables } from "../lib/openapi.js";

// Session verification middleware for protected routes
export const sessionAuth = async (
  c: Context<{ Variables: Variables }>,
  next: Next
) => {
  try {
    const cookieHeader = c.req.header("Cookie");

    // Get Auth.js session token from cookie
    const token = parseSessionTokenFromCookie(cookieHeader);

    if (!token) {
      c.set("user" as any, null);
      c.set("session" as any, null);
      await next();
      return;
    }

    try {
      // Look up Auth.js session in database
      const session = await prisma.session.findUnique({
        where: { sessionToken: token },
        include: {
          user: {
            select: {
              id: true,
              email: true,
              name: true,
              emailVerified: true,
              onboardingStatus: true,
              defaultWorkspace: true,
              role: true
            }
          }
        }
      });

      if (session && session.expires > new Date() && session.user) {
        const user: User = {
          id: session.user.id,
          email: session.user.email!,
          name: session.user.name!
        };

        c.set("user" as any, user);
        c.set("session" as any, { user, expires: session.expires });
      } else {
        c.set("user" as any, null);
        c.set("session" as any, null);
      }
    } catch (sessionError) {
      console.error("Auth.js session lookup failed:", sessionError);
      c.set("user" as any, null);
      c.set("session" as any, null);
    }
  } catch (error) {
    console.error("Session middleware error:", error);
    c.set("user" as any, null);
    c.set("session" as any, null);
  }

  await next();
};
