import { create<PERSON><PERSON><PERSON>, OpenAPIHono, z } from "@hono/zod-openapi";
import { prisma } from "@repo/database/client";
import { sendInvitationEmail } from "@repo/email/senders/send-invitation-email";

import { env } from "../lib/env.js";
import { schemas, type Variables } from "../lib/openapi.js";

// Member with aggregated data schema
const memberWithUserSchema = z.object({
  id: z.string(),
  role: z.enum(["viewer", "contributor", "admin", "owner"]),
  createdAt: z.string().datetime(),
  user: z.object({
    id: z.string(),
    name: z.string().nullable(),
    email: z.string().email().nullable(),
    image: z.string().nullable(),
    emailVerified: z.string().datetime().nullable(),
    banned: z.boolean(),
    banReason: z.string().nullable(),
    banExpires: z.string().datetime().nullable()
  }),
  invitation: z
    .object({
      id: z.string(),
      status: z.enum(["pending", "accepted", "rejected", "revoked", "expired"]),
      createdAt: z.string().datetime()
    })
    .nullable(),
  status: z.enum(["pending", "active", "suspended"])
});

// Update member schema
const updateMemberSchema = z.object({
  role: z.enum(["viewer", "contributor", "admin", "owner"]).optional(),
  ban: z.boolean().optional()
});

// Helper function to derive member status
const deriveMemberStatus = (
  member: {
    user: {
      emailVerified: Date | null;
      banned: boolean;
      banExpires: Date | null;
    };
  },
  invitation: { status: string } | null
): "pending" | "active" | "suspended" => {
  // Priority 1: Check if user is banned (Better-Auth admin plugin)
  if (member.user.banned) {
    // Check if ban has expired
    if (member.user.banExpires && member.user.banExpires < new Date()) {
      // Ban expired, treat as active
    } else {
      return "suspended";
    }
  }

  // Priority 2: Check if invitation is still pending
  if (invitation?.status === "pending") {
    return "pending";
  }

  // Priority 3: Check if user is active (invitation accepted + user registered)
  if (invitation?.status === "accepted" && member.user.emailVerified !== null) {
    return "active";
  }

  // Default to active for existing members without invitations (legacy users)
  return "active";
};

// Create members router
export const membersRouter = new OpenAPIHono<{ Variables: Variables }>();

// Get organization members with aggregated data
const getOrganizationMembersRoute = createRoute({
  method: "get",
  path: "/organizations/{slug}/members",
  tags: ["Members"],
  summary: "Get organization members",
  description:
    "Get all members of an organization with invitation status and user data",
  request: {
    params: z.object({
      slug: z.string().min(1).max(50)
    }),
    query: z.object({
      page: z.coerce.number().int().positive().default(1).optional(),
      pageSize: z.coerce
        .number()
        .int()
        .positive()
        .max(100)
        .default(20)
        .optional(),
      status: z.enum(["pending", "active", "suspended"]).optional(),
      search: z.string().optional()
    })
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: z.object({
            members: z.array(memberWithUserSchema),
            pagination: schemas.pagination
          })
        }
      },
      description: "Organization members with aggregated data"
    },
    401: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Unauthorized"
    },
    403: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Not a member of this organization"
    },
    404: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Organization not found"
    }
  }
});

// @ts-expect-error - TODO: fix this
membersRouter.openapi(getOrganizationMembersRoute, async (c) => {
  const user = c.get("user");
  const { slug } = c.req.param();
  const query = c.req.query();
  const page = Number(query.page) || 1;
  const pageSize = Number(query.pageSize) || 20;
  const skip = (page - 1) * pageSize;
  const statusFilter = query.status;
  const searchTerm = query.search;

  if (!user?.id) {
    return c.json(
      {
        error: "Authentication required",
        timestamp: new Date().toISOString()
      },
      401
    );
  }

  try {
    // First verify organization exists and user is a member
    const organization = await prisma.organization.findUnique({
      where: { slug },
      include: {
        memberships: {
          where: { userId: user.id },
          select: { role: true }
        }
      }
    });

    if (!organization) {
      return c.json(
        {
          error: "Organization not found",
          timestamp: new Date().toISOString()
        },
        404
      );
    }

    if (organization.memberships.length === 0) {
      return c.json(
        {
          error: "You are not a member of this organization",
          timestamp: new Date().toISOString()
        },
        403
      );
    }

    // Build base where clause for search
    const whereClause: {
      organizationId: string;
      user?: {
        OR: Array<
          { name: { contains: string } } | { email: { contains: string } }
        >;
      };
    } = {
      organizationId: organization.id
    };

    // Add search functionality
    if (searchTerm) {
      whereClause.user = {
        OR: [
          { name: { contains: searchTerm } },
          { email: { contains: searchTerm } }
        ]
      };
    }

    // Get members with full data
    const [membersData] = await Promise.all([
      prisma.membership.findMany({
        where: whereClause,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              image: true,
              emailVerified: true,
              banned: true,
              banReason: true,
              banExpires: true
            }
          }
        },
        skip,
        take: pageSize,
        orderBy: { createdAt: "desc" }
      })
    ]);

    // Get invitations for these users
    const userEmails = membersData
      .map((member: { user: { email: string | null } }) => member.user.email)
      .filter((email: string | null): email is string => email !== null);
    const invitations = await prisma.invitation.findMany({
      where: {
        organizationId: organization.id,
        email: { in: userEmails }
      },
      select: {
        id: true,
        email: true,
        status: true,

        createdAt: true
      }
    });

    // Create invitation lookup map
    const invitationMap = new Map();
    invitations.forEach((inv: { email: string }) => {
      invitationMap.set(inv.email, inv);
    });

    // Get pending invitations that don't have members yet
    const pendingInvitations = await prisma.invitation.findMany({
      where: {
        organizationId: organization.id,
        status: "pending",
        email: { notIn: userEmails }
      },
      select: {
        id: true,
        email: true,
        role: true,
        status: true,

        createdAt: true
      }
    });

    // Format members with aggregated data
    const formattedMembers = membersData.map(
      (member: {
        id: string;
        role: string;
        createdAt: Date;
        user: {
          email: string | null;
          name: string;
          emailVerified: Date | null;
          banExpires: Date | null;
        };
      }) => {
        const invitation = invitationMap.get(member.user.email);

        const status = deriveMemberStatus(member as any, invitation);

        return {
          id: member.id,
          role: member.role,
          createdAt: member.createdAt.toISOString(),
          user: {
            ...member.user,
            name: member.user.name || member.user.email || "Unknown", // Fallback to email if name is null
            email: member.user.email || "",
            emailVerified: member.user.emailVerified?.toISOString() || null,
            banExpires: member.user.banExpires?.toISOString() || null
          },
          invitation: invitation
            ? {
                id: invitation.id,
                status: invitation.status,

                createdAt: invitation.createdAt.toISOString()
              }
            : null,
          status
        };
      }
    );

    // Add pending invitations as pseudo-members
    const pendingMembers = pendingInvitations.map(
      (invitation: {
        id: string;
        role: string;
        email: string;
        status: string;
        createdAt: Date;
      }) => ({
        id: `invitation-${invitation.id}`,
        role: (invitation.role || "contributor") as
          | "viewer"
          | "contributor"
          | "admin"
          | "owner",
        createdAt: invitation.createdAt.toISOString(),
        user: {
          id: `pending-${invitation.id}`,
          name: null, // No name for pending invitations - display email only
          email: invitation.email || "",
          image: null,
          emailVerified: null,
          banned: false,
          banReason: null,
          banExpires: null
        },
        invitation: {
          id: invitation.id,
          status: invitation.status,

          createdAt: invitation.createdAt.toISOString()
        },
        status: "pending" as const
      })
    );

    // Combine and sort all members
    const allMembers = [...formattedMembers, ...pendingMembers];

    // Apply status filter if provided
    let filteredMembers = allMembers;
    if (statusFilter) {
      filteredMembers = allMembers.filter(
        (member) => member.status === statusFilter
      );
    }

    // Apply search filter if provided (already applied in DB query for real members, need to filter pending invitations)
    if (searchTerm && pendingMembers.length > 0) {
      const searchLower = searchTerm.toLowerCase();
      filteredMembers = filteredMembers.filter((member) => {
        if (member.id.startsWith("invitation-")) {
          return (
            member.user.name?.toLowerCase().includes(searchLower) ||
            member.user.email?.toLowerCase().includes(searchLower)
          );
        }
        return true; // Real members already filtered by DB
      });
    }

    // Sort by creation date (newest first)
    filteredMembers.sort(
      (a, b) =>
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    );

    // Apply pagination to filtered results
    const paginatedMembers = filteredMembers.slice(skip, skip + pageSize);
    const totalFilteredCount = filteredMembers.length;

    return c.json(
      {
        members: paginatedMembers,
        pagination: {
          page,
          pageSize,
          total: totalFilteredCount,
          totalPages: Math.ceil(totalFilteredCount / pageSize)
        }
      },
      200
    );
  } catch (error) {
    console.error("Error fetching organization members:", error);
    throw error;
  }
});

// Update member role or suspension status
const updateMemberRoute = createRoute({
  method: "patch",
  path: "/organizations/{slug}/members/{memberId}",
  tags: ["Members"],
  summary: "Update organization member",
  description: "Update member role or suspension status",
  request: {
    params: z.object({
      slug: z.string().min(1).max(50),
      memberId: z.string().min(1)
    }),
    body: {
      content: {
        "application/json": {
          schema: updateMemberSchema
        }
      }
    }
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: memberWithUserSchema
        }
      },
      description: "Member updated successfully"
    },
    400: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Invalid request data"
    },
    401: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Unauthorized"
    },
    403: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Insufficient permissions"
    },
    404: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Organization or member not found"
    }
  }
});

membersRouter.openapi(updateMemberRoute, async (c) => {
  const user = c.get("user");
  const { slug, memberId } = c.req.param();
  const { role, ban } = c.req.valid("json");

  if (!user?.id) {
    return c.json(
      {
        error: "Authentication required",
        timestamp: new Date().toISOString()
      },
      401
    );
  }

  try {
    // Find organization and check user permissions
    const organization = await prisma.organization.findUnique({
      where: { slug },
      include: {
        memberships: {
          where: { userId: user.id },
          select: { role: true }
        }
      }
    });

    if (!organization) {
      return c.json(
        {
          error: "Organization not found",
          timestamp: new Date().toISOString()
        },
        404
      );
    }

    const membership = organization.memberships[0];
    if (!membership) {
      return c.json(
        {
          error: "You are not a member of this organization",
          timestamp: new Date().toISOString()
        },
        403
      );
    }

    // Check if user has admin or owner role
    if (!["admin", "owner"].includes(membership.role)) {
      return c.json(
        {
          error: "Only admins and owners can update members",
          timestamp: new Date().toISOString()
        },
        403
      );
    }

    // Find target member
    const targetMember = await prisma.membership.findFirst({
      where: {
        id: memberId,
        organizationId: organization.id
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
            emailVerified: true,
            banned: true,
            banReason: true,
            banExpires: true
          }
        }
      }
    });

    if (!targetMember) {
      return c.json(
        {
          error: "Member not found",
          timestamp: new Date().toISOString()
        },
        404
      );
    }

    // Prevent self-modification for certain actions
    if (targetMember.userId === user.id) {
      if (role && role !== targetMember.role) {
        return c.json(
          {
            error: "You cannot change your own role",
            timestamp: new Date().toISOString()
          },
          403
        );
      }
    }

    // Build update data
    const updateData: {
      role?: "viewer" | "contributor" | "admin" | "owner";
    } = {};

    if (role !== undefined) {
      updateData.role = role;
    }

    // Update both member and user roles/ban status in a transaction
    const updatedMember = await prisma.$transaction(async (tx: any) => {
      // Update member role
      const member = await tx.membership.update({
        where: { id: memberId },
        data: updateData,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              image: true,
              emailVerified: true,
              banned: true,
              banReason: true,
              banExpires: true
            }
          }
        }
      });

      // Update user role if member role was changed
      if (role !== undefined) {
        // Map organization role to system-wide user role
        // admin/owner -> "admin", viewer/contributor -> "user"
        const userRole = ["viewer", "contributor"].includes(role)
          ? "user"
          : "admin";

        await tx.user.update({
          where: { id: targetMember.userId },
          data: { role: userRole }
        });
      }

      // Handle ban/unban operations
      if (ban !== undefined) {
        const userUpdateData: {
          banned: boolean;
          banReason?: string | null;
          banExpires?: Date | null;
        } = {
          banned: ban
        };

        if (ban) {
          // Ban the user
          userUpdateData.banReason = "Account suspended by administrator";
          userUpdateData.banExpires = null; // Indefinite ban
        } else {
          // Unban the user
          userUpdateData.banReason = null;
          userUpdateData.banExpires = null;
        }

        await tx.user.update({
          where: { id: targetMember.userId },
          data: userUpdateData
        });
      }

      return member;
    });

    // Get invitation data if exists
    const invitation = await prisma.invitation.findFirst({
      where: {
        organizationId: organization.id,
        email: updatedMember.user.email || ""
      },
      select: {
        id: true,
        status: true,

        createdAt: true
      }
    });

    // Derive status using the same logic as the list endpoint
    const status = deriveMemberStatus(updatedMember, invitation);

    return c.json(
      {
        id: updatedMember.id,
        role: updatedMember.role,
        createdAt: updatedMember.createdAt.toISOString(),
        user: {
          ...updatedMember.user,
          name:
            updatedMember.user.name || updatedMember.user.email || "Unknown", // Fallback to email if name is null
          email: updatedMember.user.email || "",
          emailVerified:
            updatedMember.user.emailVerified?.toISOString() || null,
          banExpires: updatedMember.user.banExpires?.toISOString() || null
        },
        invitation: invitation
          ? {
              id: invitation.id,
              status: invitation.status,

              createdAt: invitation.createdAt.toISOString()
            }
          : null,
        status
      },
      200
    );
  } catch (error) {
    console.error("Error updating member:", error);
    throw error;
  }
});

// Transfer organization ownership
const transferOwnershipRoute = createRoute({
  method: "post",
  path: "/organizations/{slug}/members/{memberId}/transfer-ownership",
  tags: ["Members"],
  summary: "Transfer organization ownership",
  description: "Transfer ownership from current owner to target member",
  request: {
    params: z.object({
      slug: z.string().min(1).max(50),
      memberId: z.string().min(1)
    })
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: z.object({
            message: z.string(),
            newOwner: memberWithUserSchema,
            formerOwner: memberWithUserSchema
          })
        }
      },
      description: "Ownership transferred successfully"
    },
    400: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Invalid request"
    },
    401: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Unauthorized"
    },
    403: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Insufficient permissions or invalid transfer"
    },
    404: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Organization or member not found"
    }
  }
});

membersRouter.openapi(transferOwnershipRoute, async (c) => {
  const user = c.get("user");
  const { slug, memberId } = c.req.param();

  if (!user?.id) {
    return c.json(
      {
        error: "Authentication required",
        timestamp: new Date().toISOString()
      },
      401
    );
  }

  try {
    // Find organization and verify current user is owner
    const organization = await prisma.organization.findUnique({
      where: { slug },
      include: {
        memberships: {
          where: { userId: user.id },
          select: { id: true, role: true, userId: true }
        }
      }
    });

    if (!organization) {
      return c.json(
        {
          error: "Organization not found",
          timestamp: new Date().toISOString()
        },
        404
      );
    }

    const currentUserMembership = organization.memberships[0];
    if (!currentUserMembership) {
      return c.json(
        {
          error: "You are not a member of this organization",
          timestamp: new Date().toISOString()
        },
        403
      );
    }

    if (currentUserMembership.role !== "owner") {
      return c.json(
        {
          error: "Only owners can transfer ownership",
          timestamp: new Date().toISOString()
        },
        403
      );
    }

    // Find target member
    const targetMember = await prisma.membership.findFirst({
      where: {
        id: memberId,
        organizationId: organization.id
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
            emailVerified: true,
            banned: true,
            banReason: true,
            banExpires: true
          }
        }
      }
    });

    if (!targetMember) {
      return c.json(
        {
          error: "Target member not found",
          timestamp: new Date().toISOString()
        },
        404
      );
    }

    // Validation checks
    if (targetMember.userId === user.id) {
      return c.json(
        {
          error: "Cannot transfer ownership to yourself",
          timestamp: new Date().toISOString()
        },
        400
      );
    }

    if (targetMember.user.banned) {
      return c.json(
        {
          error: "Cannot transfer ownership to a suspended member",
          timestamp: new Date().toISOString()
        },
        400
      );
    }

    // Check member status
    const invitation = await prisma.invitation.findFirst({
      where: {
        organizationId: organization.id,
        email: targetMember.user.email || ""
      }
    });

    const status = deriveMemberStatus(targetMember, invitation);
    if (status !== "active") {
      return c.json(
        {
          error: `Cannot transfer ownership to a ${status} member`,
          timestamp: new Date().toISOString()
        },
        400
      );
    }

    // Perform atomic ownership transfer
    const [updatedFormerOwner, updatedNewOwner] = await prisma.$transaction(
      async (tx: any) => {
        // Update current owner: owner → admin (both Member and User)
        const formerOwner = await tx.membership.update({
          where: { id: currentUserMembership.id },
          data: { role: "admin" },
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                image: true,
                emailVerified: true,
                banned: true,
                banReason: true,
                banExpires: true
              }
            }
          }
        });

        await tx.user.update({
          where: { id: user.id },
          data: { role: "admin" }
        });

        // Update target member: current role → owner (both Member and User)
        const newOwner = await tx.membership.update({
          where: { id: memberId },
          data: { role: "owner" },
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                image: true,
                emailVerified: true,
                banned: true,
                banReason: true,
                banExpires: true
              }
            }
          }
        });

        await tx.user.update({
          where: { id: targetMember.userId },
          data: { role: "admin" } // System-wide admin for organizational owners
        });

        return [formerOwner, newOwner];
      }
    );

    // Format response with invitation data
    const formerOwnerInvitation = await prisma.invitation.findFirst({
      where: {
        organizationId: organization.id,
        email: updatedFormerOwner.user.email || ""
      }
    });

    const newOwnerInvitation = await prisma.invitation.findFirst({
      where: {
        organizationId: organization.id,
        email: updatedNewOwner.user.email || ""
      }
    });

    const formatMemberResponse = (
      member: typeof updatedFormerOwner,
      invitation: {
        id: string;
        status: "pending" | "accepted" | "rejected" | "revoked" | "expired";
        createdAt: Date;
      } | null
    ) => ({
      id: member.id,
      role: member.role,
      createdAt: member.createdAt.toISOString(),
      user: {
        ...member.user,
        name: member.user.name || member.user.email || "Unknown",
        email: member.user.email || "",
        emailVerified: member.user.emailVerified?.toISOString() || null,
        banExpires: member.user.banExpires?.toISOString() || null
      },
      invitation: invitation
        ? {
            id: invitation.id,
            status: invitation.status,

            createdAt: invitation.createdAt.toISOString()
          }
        : null,
      status: deriveMemberStatus(member, invitation)
    });

    return c.json(
      {
        message: "Ownership transferred successfully",
        newOwner: formatMemberResponse(updatedNewOwner, newOwnerInvitation),
        formerOwner: formatMemberResponse(
          updatedFormerOwner,
          formerOwnerInvitation
        )
      },
      200
    );
  } catch (error) {
    console.error("Error transferring ownership:", error);
    throw error;
  }
});

// Create organization invitations
const createInvitationsRoute = createRoute({
  method: "post",
  path: "/organizations/{slug}/invitations",
  tags: ["Members"],
  summary: "Create organization invitations",
  description: "Send invitations to join an organization",
  request: {
    params: z.object({
      slug: z.string().min(1).max(50)
    }),
    body: {
      content: {
        "application/json": {
          schema: z.object({
            invitations: z
              .array(
                z.object({
                  email: z.string().email(),
                  role: z
                    .enum(["viewer", "contributor", "admin", "owner"])
                    .default("contributor")
                })
              )
              .min(1)
              .max(10)
          })
        }
      }
    }
  },
  responses: {
    201: {
      content: {
        "application/json": {
          schema: z.object({
            success: z.boolean(),
            invitations: z.array(
              z.object({
                id: z.string(),
                email: z.string().email(),
                role: z.enum(["viewer", "contributor", "admin", "owner"]),
                status: z.literal("pending"),
                createdAt: z.string().datetime()
              })
            ),
            message: z.string()
          })
        }
      },
      description: "Invitations created successfully"
    },
    400: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Invalid request data"
    },
    401: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Unauthorized"
    },
    403: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Insufficient permissions"
    },
    404: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Organization not found"
    }
  }
});

// @ts-expect-error Complex Hono OpenAPI type inference issue - runtime behavior is correct
membersRouter.openapi(createInvitationsRoute, async (c) => {
  const user = c.get("user");
  const { slug } = c.req.param();
  const { invitations } = c.req.valid("json");

  if (!user?.id) {
    return c.json(
      {
        error: "Authentication required",
        timestamp: new Date().toISOString()
      },
      401
    );
  }

  try {
    // Find organization and check user permissions
    const organization = await prisma.organization.findUnique({
      where: { slug },
      include: {
        memberships: {
          where: { userId: user.id },
          select: { role: true }
        }
      }
    });

    if (!organization) {
      return c.json(
        {
          error: "Organization not found",
          timestamp: new Date().toISOString()
        },
        404
      );
    }

    const membership = organization.memberships[0];
    if (!membership) {
      return c.json(
        {
          error: "You are not a member of this organization",
          timestamp: new Date().toISOString()
        },
        403
      );
    }

    // Check if user has permission to invite (admin or owner)
    if (!["admin", "owner"].includes(membership.role)) {
      return c.json(
        {
          error: "Only admins and owners can send invitations",
          timestamp: new Date().toISOString()
        },
        403
      );
    }

    // Filter out duplicate emails and existing members
    const uniqueEmails = Array.from(
      new Set(invitations.map((inv) => inv.email.toLowerCase()))
    );

    // Check for existing members or pending invitations
    const existingMembers = await prisma.membership.findMany({
      where: {
        organizationId: organization.id,
        user: {
          email: { in: uniqueEmails }
        }
      },
      select: { user: { select: { email: true } } }
    });

    const existingInvitations = await prisma.invitation.findMany({
      where: {
        organizationId: organization.id,
        email: { in: uniqueEmails },
        status: "pending"
      },
      select: { email: true }
    });

    const existingEmails = new Set([
      ...existingMembers
        .map((m: { user: { email: string | null } }) =>
          m.user.email?.toLowerCase()
        )
        .filter(Boolean),
      ...existingInvitations.map((inv: { email: string }) =>
        inv.email.toLowerCase()
      )
    ]);

    // Filter out existing emails
    const newInvitations = invitations.filter(
      (inv) => !existingEmails.has(inv.email.toLowerCase())
    );

    if (newInvitations.length === 0) {
      return c.json(
        {
          error:
            "All provided emails are already members or have pending invitations",
          timestamp: new Date().toISOString()
        },
        400
      );
    }

    // Create invitations
    const createdInvitations = await prisma.$transaction(async (tx: any) => {
      const invites = await Promise.all(
        newInvitations.map((invitation) =>
          tx.invitation.create({
            data: {
              organizationId: organization.id,
              email: invitation.email.toLowerCase(),
              role: invitation.role,
              invitedByUserId: user.id,
              lastSentAt: new Date()
            },
            select: {
              id: true,
              email: true,
              role: true,
              createdAt: true,
              token: true
            }
          })
        )
      );
      return invites;
    });

    // Send invitation emails
    try {
      // Get inviting user info for the email
      const invitingUser = await prisma.user.findUnique({
        where: { id: user.id },
        select: { name: true, email: true }
      });

      // Send emails in parallel (but don't wait for them to avoid blocking the response)
      Promise.all(
        createdInvitations.map(
          async (invitation: { id: string; email: string; token: string }) => {
            try {
              // Generate invitation link with id
              const inviteLink = `${env.FRONTEND_URL || "http://localhost:3000"}/accept-invitation/${invitation.id}`;

              await sendInvitationEmail({
                recipient: invitation.email,
                invitedByName:
                  invitingUser?.name || invitingUser?.email || "Team member",
                organizationName: organization.name,
                inviteLink
              });
            } catch (emailError) {
              console.error(
                `Failed to send invitation email to ${invitation.email}:`,
                emailError
              );
              // Don't fail the entire request if email sending fails
            }
          }
        )
      ).catch((error) => {
        console.error("Error sending invitation emails:", error);
        // Log but don't fail the request
      });
    } catch (error) {
      console.error("Error preparing invitation emails:", error);
      // Don't fail the request if email preparation fails
    }

    const responseData = {
      success: true,
      invitations: createdInvitations.map(
        (inv: {
          id: string;
          email: string;
          role: string;
          createdAt: Date;
        }) => ({
          id: inv.id,
          email: inv.email,
          role: inv.role as "viewer" | "contributor" | "admin" | "owner",
          status: "pending" as const,
          createdAt: inv.createdAt.toISOString()
        })
      ),
      message: `${createdInvitations.length} invitation${createdInvitations.length > 1 ? "s" : ""} sent successfully`
    };

    return c.json(responseData, 201);
  } catch (error) {
    console.error("Error creating invitations:", error);
    return c.json(
      {
        error: "Failed to create invitations",
        timestamp: new Date().toISOString()
      },
      500
    );
  }
});

// Remove member from organization
const removeMemberRoute = createRoute({
  method: "delete",
  path: "/organizations/{slug}/members/{memberId}",
  tags: ["Members"],
  summary: "Remove organization member",
  description: "Remove member from organization or cancel invitation",
  request: {
    params: z.object({
      slug: z.string().min(1).max(50),
      memberId: z.string().min(1)
    })
  },
  responses: {
    204: {
      description: "Member removed successfully"
    },
    401: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Unauthorized"
    },
    403: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Insufficient permissions"
    },
    404: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Organization or member not found"
    }
  }
});

membersRouter.openapi(removeMemberRoute, async (c) => {
  const user = c.get("user");
  const { slug, memberId } = c.req.param();

  if (!user?.id) {
    return c.json(
      {
        error: "Authentication required",
        timestamp: new Date().toISOString()
      },
      401
    );
  }

  try {
    // Find organization and check user permissions
    const organization = await prisma.organization.findUnique({
      where: { slug },
      include: {
        memberships: {
          where: { userId: user.id },
          select: { role: true }
        }
      }
    });

    if (!organization) {
      return c.json(
        {
          error: "Organization not found",
          timestamp: new Date().toISOString()
        },
        404
      );
    }

    const membership = organization.memberships[0];
    if (!membership) {
      return c.json(
        {
          error: "You are not a member of this organization",
          timestamp: new Date().toISOString()
        },
        403
      );
    }

    // Check if user has admin or owner role
    if (!["admin", "owner"].includes(membership.role)) {
      return c.json(
        {
          error: "Only admins and owners can remove members",
          timestamp: new Date().toISOString()
        },
        403
      );
    }

    // Check if this is an invitation ID (for pending invitations)
    if (memberId.startsWith("invitation-")) {
      const invitationId = memberId.replace("invitation-", "");

      // Find and cancel the invitation
      const invitation = await prisma.invitation.findFirst({
        where: {
          id: invitationId,
          organizationId: organization.id
        }
      });

      if (!invitation) {
        return c.json(
          {
            error: "Invitation not found",
            timestamp: new Date().toISOString()
          },
          404
        );
      }

      // Cancel the invitation
      await prisma.invitation.update({
        where: { id: invitationId },
        data: { status: "revoked" }
      });

      return c.body(null, 204);
    }

    // Handle regular member removal
    const targetMember = await prisma.membership.findFirst({
      where: {
        id: memberId,
        organizationId: organization.id
      }
    });

    if (!targetMember) {
      return c.json(
        {
          error: "Member not found",
          timestamp: new Date().toISOString()
        },
        404
      );
    }

    // Prevent owner from removing themselves if they're the only owner
    if (targetMember.userId === user.id && targetMember.role === "owner") {
      const ownerCount = await prisma.membership.count({
        where: {
          organizationId: organization.id,
          role: "owner"
        }
      });

      if (ownerCount === 1) {
        return c.json(
          {
            error: "Cannot remove the last owner. Transfer ownership first.",
            timestamp: new Date().toISOString()
          },
          403
        );
      }
    }

    // Remove member
    await prisma.membership.delete({
      where: { id: memberId }
    });

    return c.body(null, 204);
  } catch (error) {
    console.error("Error removing member:", error);
    throw error;
  }
});

// Resend invitation
const resendInvitationRoute = createRoute({
  method: "post",
  path: "/organizations/{slug}/invitations/{invitationId}/resend",
  tags: ["Members"],
  summary: "Resend organization invitation",
  description: "Resend an existing invitation email",
  request: {
    params: z.object({
      slug: z.string().min(1).max(50),
      invitationId: z.string().min(1)
    })
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: z.object({
            success: z.boolean(),
            message: z.string()
          })
        }
      },
      description: "Invitation resent successfully"
    },
    401: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Unauthorized"
    },
    403: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Insufficient permissions"
    },
    404: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Organization or invitation not found"
    },
    500: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Internal server error"
    }
  }
});

membersRouter.openapi(resendInvitationRoute, async (c) => {
  const user = c.get("user");
  const { slug, invitationId } = c.req.param();

  if (!user?.id) {
    return c.json(
      {
        error: "Authentication required",
        timestamp: new Date().toISOString()
      },
      401
    );
  }

  try {
    // Find organization and check user permissions
    const organization = await prisma.organization.findUnique({
      where: { slug },
      include: {
        memberships: {
          where: { userId: user.id },
          select: { role: true }
        }
      }
    });

    if (!organization) {
      return c.json(
        {
          error: "Organization not found",
          timestamp: new Date().toISOString()
        },
        404
      );
    }

    const membership = organization.memberships[0];
    if (!membership) {
      return c.json(
        {
          error: "You are not a member of this organization",
          timestamp: new Date().toISOString()
        },
        403
      );
    }

    // Check if user has permission to resend invitations (admin or owner)
    if (!["admin", "owner"].includes(membership.role)) {
      return c.json(
        {
          error: "Only admins and owners can resend invitations",
          timestamp: new Date().toISOString()
        },
        403
      );
    }

    // Find the invitation
    const invitation = await prisma.invitation.findFirst({
      where: {
        id: invitationId,
        organizationId: organization.id,
        status: "pending"
      }
    });

    if (!invitation) {
      return c.json(
        {
          error: "Invitation not found or already processed",
          timestamp: new Date().toISOString()
        },
        404
      );
    }

    // Update the invitation's lastSentAt timestamp
    await prisma.invitation.update({
      where: { id: invitationId },
      data: { lastSentAt: new Date() }
    });

    // Send the invitation email
    try {
      // Get inviting user info for the email
      const invitingUser = await prisma.user.findUnique({
        where: { id: user.id },
        select: { name: true, email: true }
      });

      // Generate invitation link
      const inviteLink = `${env.FRONTEND_URL || "http://localhost:3000"}/accept-invitation/${invitation.id}`;

      await sendInvitationEmail({
        recipient: invitation.email,
        invitedByName:
          invitingUser?.name || invitingUser?.email || "Team member",
        organizationName: organization.name,
        inviteLink
      });
    } catch (emailError) {
      console.error(
        `Failed to send invitation email to ${invitation.email}:`,
        emailError
      );
      // Don't fail the entire request if email sending fails
    }

    return c.json(
      {
        success: true,
        message: "Invitation resent successfully"
      },
      200
    );
  } catch (error) {
    console.error("Error resending invitation:", error);
    return c.json(
      {
        error: "Failed to resend invitation",
        timestamp: new Date().toISOString()
      },
      500
    );
  }
});
