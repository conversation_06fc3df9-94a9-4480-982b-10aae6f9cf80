/**
 * @fileoverview Authentication routes for the Centaly application backend.
 *
 * This module provides HTTP endpoints for authentication-related operations including:
 * - Email verification and automatic session creation
 * - Session validation for frontend-backend communication
 *
 * The auth system uses a hybrid approach:
 * - Frontend: Auth.js v5 with custom server actions
 * - Backend: Direct Auth.js database session validation
 * - No JWTs: Sessions stored in database with token-based lookups
 *
 * @see {@link ../../../AUTHENTICATION_FLOWS.md} For complete authentication flow documentation
 */

import { createRoute, OpenAPIHono, z } from "@hono/zod-openapi";
import {
  generateSessionToken,
  getSessionExpiryFromNow
} from "@repo/auth/session";
import { prisma } from "@repo/database/client";

import {
  parseSessionTokenFromCookie,
  symmetricDecrypt,
  validateVerificationToken,
  type DecodedVerificationToken
} from "../lib/auth-utils.js";
import { env, isDevelopment } from "../lib/env.js";
import { schemas, type Variables } from "../lib/openapi.js";

/**
 * Zod schema for user data returned in API responses.
 * Matches the Auth.js User model with additional Centaly-specific fields.
 */
const userResponseSchema = z.object({
  id: z.string(),
  email: z.string(),
  name: z.string(),
  emailVerified: z.string().datetime().nullable(),
  onboardingStatus: z.enum(["incomplete", "workspace", "invite", "complete"]),
  defaultWorkspace: z.string().nullable(),
  role: z.string()
});

/**
 * Zod schema for session data returned by the session endpoint.
 * Contains user information and session expiry date.
 */
const sessionResponseSchema = z.object({
  user: userResponseSchema.nullable(),
  expires: z.string().optional()
});

/**
 * Zod schema for email verification requests.
 * Used by the frontend's email verification handler component.
 */
const emailVerificationRequestSchema = z.object({
  token: z.string(),
  callbackUrl: z.string().optional()
});

/**
 * Zod schema for successful email verification responses.
 * Returns user data and redirect URL for post-verification navigation.
 */
const emailVerificationResponseSchema = z.object({
  success: z.boolean(),
  user: userResponseSchema,
  callbackUrl: z.string()
});

/**
 * Authentication router instance.
 * Handles public auth endpoints that don't require session middleware.
 * Mounted at `/api/auth/*` in the main application.
 */
export const authRouter = new OpenAPIHono<{ Variables: Variables }>();

/**
 * OpenAPI route definition for email verification endpoint.
 *
 * This endpoint is part of the email verification flow where users click a link
 * in their verification email and get automatically signed in to the application.
 *
 * **Flow Context:**
 * 1. User receives verification email with encrypted token
 * 2. Frontend component (`email-verification-handler.tsx`) calls this endpoint
 * 3. Backend validates token and creates Auth.js database session
 * 4. Session cookie is set for cross-domain authentication
 * 5. User is redirected to onboarding or dashboard
 *
 * **Security Features:**
 * - 5-minute token expiry for security
 * - AES-256 symmetric encryption of tokens
 * - Validates email verification status in database
 * - Creates Auth.js compatible sessions with 30-day expiry
 *
 * @see {@link apps/dashboard/src/features/auth/components/email-verification-handler.tsx} Frontend component
 * @see {@link apps/dashboard/src/features/auth/actions/verify-email-with-token.ts} Token generation
 */
const emailVerificationRoute = createRoute({
  method: "post",
  path: "/auth/signin/email-verification",
  tags: ["Authentication"],
  summary: "Verify email and create session",
  description: "Verify email verification token and create user session",
  request: {
    body: {
      content: {
        "application/x-www-form-urlencoded": {
          schema: emailVerificationRequestSchema
        }
      }
    }
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: emailVerificationResponseSchema
        }
      },
      description: "Email verification successful"
    },
    400: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Invalid or expired token"
    },
    500: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Internal server error"
    }
  }
});

/**
 * Email verification endpoint handler.
 *
 * **Purpose:**
 * Validates encrypted email verification tokens and creates authenticated sessions.
 * This enables seamless user sign-in after email verification without requiring
 * additional login credentials.
 *
 * **Used By:**
 * - `apps/dashboard/src/features/auth/components/email-verification-handler.tsx`
 * - Called when users click verification links in their email
 *
 * **Process:**
 * 1. Decrypt and validate the encrypted token (5-minute expiry)
 * 2. Verify user exists and email is verified in database
 * 3. Generate Auth.js compatible session token (ULID-based)
 * 4. Create session record in database with 30-day expiry
 * 5. Set secure session cookie for cross-domain authentication
 * 6. Return user data and callback URL for frontend redirection
 *
 * **Security:**
 * - Token encryption using AUTH_SECRET with AES-256
 * - HttpOnly cookies prevent XSS attacks
 * - SameSite=Lax prevents CSRF attacks
 * - Secure flag in production for HTTPS-only cookies
 * - Short token expiry (5 minutes) limits attack window
 *
 * @param {Context} c - Hono context object
 * @returns {Promise<Response>} JSON response with user data or error
 */
authRouter.openapi(emailVerificationRoute, async (c) => {
  try {
    console.log("EmailVerification: Received request");

    const body = await c.req.parseBody();
    const token = body.token as string;
    const callbackUrl = body.callbackUrl as string;

    if (!token) {
      console.error("EmailVerification: No token provided");
      return c.json(
        {
          error: "No token provided",
          timestamp: new Date().toISOString()
        },
        400
      );
    }

    console.log("EmailVerification: Attempting to decrypt token");

    try {
      const decoded: DecodedVerificationToken = JSON.parse(
        symmetricDecrypt(token, env.AUTH_SECRET!)
      );

      console.log("EmailVerification: Decoded token:", {
        userId: decoded.userId,
        email: decoded.email,
        purpose: decoded.purpose,
        expires: decoded.expires
      });

      // Validate the token expiry and purpose
      if (!validateVerificationToken(decoded)) {
        console.error("EmailVerification: Token validation failed");
        return c.json(
          {
            error: "Invalid or expired token",
            timestamp: new Date().toISOString()
          },
          400
        );
      }

      console.log("EmailVerification: Looking up user:", decoded.userId);
      const user = await prisma.user.findUnique({
        where: { id: decoded.userId },
        select: {
          id: true,
          email: true,
          name: true,
          emailVerified: true
        }
      });

      console.log("EmailVerification: User found:", {
        userExists: !!user,
        emailVerified: user?.emailVerified
      });

      // Ensure user exists and email is verified
      if (!user || !user.emailVerified) {
        console.error(
          "EmailVerification: User not found or email not verified"
        );
        return c.json(
          {
            error: "User not found or email not verified",
            timestamp: new Date().toISOString()
          },
          400
        );
      }

      console.log("EmailVerification: Creating Auth.js database session");

      // Generate Auth.js compatible session token (ULID-based)
      const sessionToken = generateSessionToken();
      const expires = getSessionExpiryFromNow();

      // Get user's oldest organization membership to set as active
      // const oldestMembership = await prisma.membership.findFirst({
      //   where: {
      //     userId: user.id,
      //     membershipStatus: "active"
      //   },
      //   orderBy: { createdAt: "asc" },
      //   select: { organizationId: true }
      // });

      // Create Auth.js database session record with activeOrganizationId
      await prisma.session.create({
        data: {
          sessionToken,
          userId: user.id,
          expires
          // activeOrganizationId: oldestMembership?.organizationId || null
        }
      });

      // Set Auth.js compatible session cookie with security flags
      const maxAge = 60 * 60 * 24 * 30; // 30 days
      const authJsCookie = `next-auth.session-token=${sessionToken}; HttpOnly; Secure=${!isDevelopment}; SameSite=Lax; Path=/; Max-Age=${maxAge}`;
      c.header("Set-Cookie", authJsCookie);

      console.log("EmailVerification: Authentication successful");

      // Get full user data for response including onboarding status
      const fullUser = await prisma.user.findUnique({
        where: { id: user.id },
        select: {
          id: true,
          email: true,
          name: true,
          emailVerified: true,
          onboardingStatus: true,
          defaultWorkspace: true,
          role: true
        }
      });

      // Return success response with user data and redirect URL
      return c.json(
        {
          success: true,
          user: {
            id: fullUser!.id as string,
            email: fullUser!.email! as string,
            name: fullUser!.name! as string,
            emailVerified: (fullUser!.emailVerified?.toISOString() || null) as
              | string
              | null,
            onboardingStatus: fullUser!.onboardingStatus as
              | "incomplete"
              | "workspace"
              | "invite"
              | "complete",
            defaultWorkspace: fullUser!.defaultWorkspace as string | null,
            role: fullUser!.role as string
          },
          callbackUrl: callbackUrl || "/onboarding/workspace"
        },
        200
      );
    } catch (decryptError) {
      console.error("Error decoding verification token:", decryptError);
      return c.json(
        {
          error: "Invalid token format",
          timestamp: new Date().toISOString()
        },
        400
      );
    }
  } catch (error) {
    console.error("EmailVerification: Unexpected error:", error);
    return c.json(
      {
        error: "Internal server error",
        timestamp: new Date().toISOString()
      },
      500
    );
  }
});

/**
 * OpenAPI route definition for session validation endpoint.
 *
 * This endpoint provides session validation for both frontend and backend components.
 * It validates Auth.js session tokens from cookies and returns current user information.
 *
 * **Flow Context:**
 * - Used by frontend session utilities for server-side validation
 * - Called by middleware to validate API requests
 * - Provides consistent session data across frontend and backend
 *
 * **Security Features:**
 * - Validates session tokens against database records
 * - Checks session expiry automatically
 * - Returns null for invalid/expired sessions (no error thrown)
 * - Graceful degradation for missing cookies
 *
 * @see {@link apps/dashboard/src/features/auth/utils/session.ts} Frontend session utilities
 * @see {@link apps/backend/src/middleware/session-auth.ts} Session middleware
 */
const sessionRoute = createRoute({
  method: "get",
  path: "/auth/session",
  tags: ["Authentication"],
  summary: "Get current session",
  description: "Get the current user session information",
  responses: {
    200: {
      content: {
        "application/json": {
          schema: sessionResponseSchema
        }
      },
      description: "Session information"
    },
    500: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Internal server error"
    }
  }
});

/**
 * Session validation endpoint handler.
 *
 * **Purpose:**
 * Validates Auth.js session tokens and returns current user information.
 * This endpoint is used by both frontend session utilities and backend middleware
 * to maintain consistent authentication state across the application.
 *
 * **Used By:**
 * - `apps/dashboard/src/features/auth/utils/session.ts` - Frontend session validation
 * - `apps/backend/src/middleware/session-auth.ts` - Backend session middleware
 * - Next.js server components that need session data
 * - Client components via API calls for session status
 *
 * **Process:**
 * 1. Extract session token from Cookie header
 * 2. Look up session in Auth.js database table
 * 3. Validate session expiry against current time
 * 4. Return user data if valid, null if invalid/expired
 * 5. Handle errors gracefully without exposing sensitive data
 *
 * **Security:**
 * - No authentication required (public endpoint)
 * - Graceful degradation for missing/invalid tokens
 * - No user enumeration (always returns 200 status)
 * - Session token validation against database
 * - Automatic expiry checking
 *
 * **Returns:**
 * - Valid session: `{ user: UserData, expires: string }`
 * - Invalid/expired/missing: `{ user: null }`
 * - Server error: `{ error: string, timestamp: string }` with 500 status
 *
 * @param {Context} c - Hono context object
 * @returns {Promise<Response>} JSON response with session data or null
 */
authRouter.openapi(sessionRoute, async (c) => {
  try {
    const cookieHeader = c.req.header("Cookie");
    const token = parseSessionTokenFromCookie(cookieHeader);

    // Return null user if no session token present
    if (!token) {
      return c.json({ user: null }, 200);
    }

    try {
      // Look up Auth.js session in database with user details
      const session = await prisma.session.findUnique({
        where: { sessionToken: token },
        include: {
          user: {
            select: {
              id: true,
              email: true,
              name: true,
              emailVerified: true,
              onboardingStatus: true,
              defaultWorkspace: true,
              role: true
            }
          }
        }
      });

      // Validate session exists, hasn't expired, and has valid user
      if (session && session.expires > new Date() && session.user) {
        return c.json(
          {
            user: {
              id: session.user.id as string,
              email: session.user.email! as string,
              name: session.user.name! as string,
              emailVerified: (session.user.emailVerified?.toISOString() ||
                null) as string | null,
              onboardingStatus: session.user.onboardingStatus as
                | "incomplete"
                | "workspace"
                | "invite"
                | "complete",
              defaultWorkspace: session.user.defaultWorkspace as string | null,
              role: session.user.role as string
            },
            expires: session.expires.toISOString()
          },
          200
        );
      } else {
        // Session not found, expired, or missing user - return null
        return c.json({ user: null }, 200);
      }
    } catch (sessionError) {
      console.error(
        "Auth.js session lookup failed in session endpoint:",
        sessionError
      );
      // Database errors should not expose session status - return null
      return c.json({ user: null }, 200);
    }
  } catch (error) {
    console.error("Session endpoint error:", error);
    return c.json(
      {
        error: "Internal server error",
        timestamp: new Date().toISOString()
      },
      500
    );
  }
});
