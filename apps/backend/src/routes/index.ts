import { OpenAPIHono } from "@hono/zod-openapi";

import { authRouter } from "./auth.js";
import { usersRouter } from "./users.js";
import { invitationsRouter } from "./invitations.js";
import { organizationsRouter } from "./organizations.js";
import { todosRouter } from "./todos.js";
import { membersRouter } from "./members.js";

// Export function to register all routes
export function registerRoutes(api: OpenAPIHono) {
  // Register all route modules here
  api.route("/", usersRouter);
  api.route("/", invitationsRouter);
  api.route("/", organizationsRouter);
  api.route("/", todosRouter);
  api.route("/", membersRouter);

  // Add more routes as needed
  // api.route("/", teamsRouter);
  // api.route("/", projectsRouter);
}

// Export auth router separately since it mounts at /api level
export { authRouter } from "./auth.js";
