{"name": "email-preview", "version": "0.0.0", "type": "module", "private": true, "packageManager": "pnpm@9.15.4", "scripts": {"dev": "email dev --port 3003", "export": "email export", "clean": "git clean -xdf .cache .turbo build dist node_modules", "lint": "eslint .", "lint:fix": "eslint --fix ."}, "dependencies": {"@react-email/components": "^0.0.32", "@repo/email": "workspace:*", "react": "^19.1.1", "react-email": "3.0.6"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@repo/prettier-config": "workspace:*", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2"}, "prettier": "@repo/prettier-config"}