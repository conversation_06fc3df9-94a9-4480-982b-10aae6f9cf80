// import { cookies } from "next/headers";
import type { NextAuthConfig } from "next-auth";

import { adapter } from "./adapter";
// import { AuthCookies } from "./cookies";
// import { Provider } from "./providers.types";
// import { getRedirectToTotp } from "./redirect";
import { generateSessionToken, getSessionExpiryFromNow } from "./session";

export const callbacks: NextAuthConfig["callbacks"] = {
  // async signIn({ user, account, profile }): Promise<string | boolean> {
  //   if (!account) {
  //     return false;
  //   }
  //   // All Credentials Provider
  //   if (account.type === "credentials") {
  //     if (!user || !user.id) {
  //       return false;
  //     }

  //     // Only username/password provider
  //     if (account.provider === Provider.Credentials) {
  //       return getRedirectToTotp(user.id);
  //     }

  //     const sessionToken = generateSessionToken();
  //     const sessionExpiry = getSessionExpiryFromNow();

  //     const createdSession = await adapter.createSession!({
  //       sessionToken: sessionToken,
  //       userId: user.id,
  //       expires: sessionExpiry
  //     });

  //     if (!createdSession) {
  //       return false;
  //     }

  //     const cookieStore = await cookies();
  //     cookieStore.set({
  //       name: AuthCookies.SessionToken,
  //       value: sessionToken,
  //       expires: sessionExpiry
  //     });

  //     // already authorized
  //     return true;
  //   }

  //   // All OAuth Providers
  //   if (!account.provider || !profile) {
  //     return false;
  //   }

  //   if (user?.name) {
  //     user.name = user.name.substring(0, 64);
  //   }
  //   if (profile.name) {
  //     profile.name = profile.name.substring(0, 64);
  //   }

  //   return true;
  // },
  async jwt({ token, trigger, account, user }) {
    if ((trigger === "signIn" || trigger === "signUp") && account) {
      token.accessToken = account.access_token;

      if (account.type === "credentials" && user.id) {
        const expires = getSessionExpiryFromNow();
        const sessionToken = generateSessionToken();

        const session = await adapter.createSession!({
          userId: user.id,
          sessionToken,
          expires
        });

        token.sessionId = session.sessionToken;
      }
    }

    // Let's not allow the client to indirectly update the token using useSession().update()
    if (trigger === "update") {
      return token;
    }

    return token;
  },
  async session({ trigger, session, user }) {
    if (session && user) {
      session.user.id = user.id;
    }

    // Let's not allow the client to update the session using useSession().update()
    if (trigger === "update") {
      return session;
    }

    return session;
  }
};
