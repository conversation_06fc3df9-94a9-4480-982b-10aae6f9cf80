import { prisma } from "@repo/database/client";
import { sendConnectedAccountSecurityAlertEmail } from "@repo/email/senders/send-connected-account-security-alert-email";
// import { sendWelcomeEmail } from "@repo/email/senders/send-welcome-email";
import type { NextAuthConfig } from "next-auth";

// import { verifyEmail } from "./verification";

// TODO: getStartedLink should be dynamic based on the user's organization, was route to organizations.Index

export const events = {
  // async signIn({ user, isNewUser }) {
  //   if (user && user.id) {
  //     await prisma.user.update({
  //       where: { id: user.id },
  //       data: { lastLogin: new Date() },
  //       select: {
  //         id: true // SELECT NONE
  //       }
  //     });

  //     if (isNewUser && user.email) {
  //       await verifyEmail(user.email);
  //       if (user.name) {
  //         await sendWelcomeEmail({
  //           recipient: user.email!,
  //           appName: "Centaly",
  //           name: user.name,
  //           getStartedLink: "/home"
  //         });
  //       }
  //     }
  //   }
  // },
  // async signOut(message) {
  //   if ("session" in message && message.session?.sessionToken) {
  //     await prisma.session.deleteMany({
  //       where: { sessionToken: message.session.sessionToken }
  //     });
  //   }
  // },
  async linkAccount({ user, account }) {
    if (user && user.name && user.email && account && account.provider) {
      // Here we check if the user just has been created using an OAuth provider
      // - If yes -> No need to send out security alert
      // - If no (which means linked using an existing account) -> Send out security alert

      const newUser = await prisma.user.findFirst({
        where: {
          email: user.email,
          lastLogin: null
        },
        include: {
          _count: {
            select: {
              accounts: true
            }
          }
        }
      });

      const isNewUser = newUser && newUser._count.accounts < 2;
      if (!isNewUser) {
        try {
          await sendConnectedAccountSecurityAlertEmail({
            recipient: user.email,
            appName: "Centaly",
            name: user.name,
            action: "connected",
            provider: account.provider
          });
        } catch (e) {
          console.error(e);
        }
      }
    }
  }
} satisfies NextAuthConfig["events"];
