import { hasBasePath } from "next/dist/client/has-base-path";
import { removeBasePath } from "next/dist/client/remove-base-path";
import { workUnitAsyncStorage } from "next/dist/server/app-render/work-unit-async-storage.external";
import { cookies } from "next/headers";

// import { addMinutes } from "date-fns";

// import { TOTP_AND_RECOVERY_CODES_EXPIRY_MINUTES } from "./constants";
import { AuthCookies } from "./cookies";

// import { symmetricEncrypt } from "./encryption";

export function getRedirectToSignIn(): string {
  const callbackUrl = getRequestStoragePathname();

  return callbackUrl
    ? `${"/api/signin"}?${new URLSearchParams({ callbackUrl })}`
    : `${"/api/signin"}`;
}

export async function getRedirectAfterSignIn(): Promise<string> {
  // Get the stored callback URL from cookies.
  const cookieStore = await cookies();
  const callbackUrl = cookieStore.get(AuthCookies.CallbackUrl)?.value;

  // Previously, if no callback was provided, we were reading an organization slug
  // from the cookies and using it to construct a redirect URL.
  // Now that we have removed organization slug functionality,
  // we fall back to a default homepage.
  return callbackUrl || "/home";
}

// export function getRedirectToTotp(userId: string): string {
//   const key = process.env.AUTH_SECRET;
//   const token = symmetricEncrypt(userId, key!);
//   const expiry = symmetricEncrypt(
//     addMinutes(
//       new Date(),
//       TOTP_AND_RECOVERY_CODES_EXPIRY_MINUTES
//     ).toISOString(),
//     key!
//   );

//   return `/totp?token=${encodeURIComponent(token)}&expiry=${encodeURIComponent(expiry)}`;
// }

export function getRequestStoragePathname(): string | null {
  const store = workUnitAsyncStorage.getStore();
  if (!store || store.type !== "request") {
    return null;
  }

  const url = new URL(store.url.pathname + store.url.search, "http://n");
  if (hasBasePath(url.pathname)) {
    return removeBasePath(url.pathname) + url.search;
  }

  return url.pathname + url.search;
}
