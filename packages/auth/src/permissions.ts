import { Role } from "@repo/database";
import { prisma } from "@repo/database/client";

import { NotFoundError } from "./errors";

export async function isOrganizationOwner(
  userId: string,
  organizationId: string
): Promise<boolean> {
  const membership = await prisma.membership.findFirst({
    where: { userId, organizationId },
    select: { role: true }
  });
  if (!membership) {
    throw new NotFoundError("Membership not found");
  }

  return membership.role === Role.owner;
}

export async function isOrganizationAdmin(
  userId: string,
  organizationId: string
): Promise<boolean> {
  const membership = await prisma.membership.findFirst({
    where: { userId, organizationId },
    select: { role: true }
  });
  if (!membership) {
    throw new NotFoundError("Membership not found");
  }

  return membership.role === Role.admin;
}

export async function isOrganizationContributor(
  userId: string,
  organizationId: string
): Promise<boolean> {
  const membership = await prisma.membership.findFirst({
    where: { userId, organizationId },
    select: { role: true }
  });
  if (!membership) {
    throw new NotFoundError("Membership not found");
  }

  return membership.role === Role.contributor;
}

export async function isOrganizationViewer(
  userId: string,
  organizationId: string
): Promise<boolean> {
  const membership = await prisma.membership.findFirst({
    where: { userId, organizationId },
    select: { role: true }
  });
  if (!membership) {
    throw new NotFoundError("Membership not found");
  }

  return membership.role === Role.viewer;
}
