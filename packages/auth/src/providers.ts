// import { Authenticator } from "@otplib/core";
// import { createDigest, createRandomBytes } from "@otplib/plugin-crypto";
// import { keyDecoder, keyEncoder } from "@otplib/plugin-thirty-two";
import { prisma } from "@repo/database/client";
import { isBefore, isValid } from "date-fns";
import { type NextAuthConfig } from "next-auth";
import CredentialsProvider from "next-auth/providers/credentials";

// import { symmetricDecrypt, symmetricEncrypt } from "./encryption";
import {
  IncorrectEmailOrPasswordError,
  // IncorrectRecoveryCodeError,
  // IncorrectTotpCodeError,
  InternalServerError,
  // MissingRecoveryCodesError,
  RateLimitExceededError,
  // RequestExpiredError,
  UnverifiedEmailError
} from "./errors";
import { inMemoryRateLimiter } from "./in-memory";
import { verifyPassword } from "./password";
import { Provider } from "./providers.types";
import {
  logInSchema
  // submitRecoveryCodeSchema,
  // submitTotpCodeSchema
} from "./schemas";

function checkRateLimitAndThrowError(uniqueIdentifier: string): void {
  // Built-in rate limiter to help manage traffic and prevent abuse.
  // Does not support serverless rate limiting, because the storage is in-memory.
  const limiter = inMemoryRateLimiter({
    intervalInMs: 60 * 1000 // 1 minute
  });
  const result = limiter.check(10, uniqueIdentifier); // 10 requests per minute
  if (result.isRateLimited) {
    throw new RateLimitExceededError();
  }
}

export const providers = [
  // CredentialsProvider({
  //   id: Provider.Credentials,
  //   name: Provider.Credentials,
  //   credentials: {
  //     email: { label: "Email", type: "text" },
  //     password: { label: "Password", type: "password" }
  //   },
  //   async authorize(credentials) {
  //     if (!credentials) {
  //       console.error(`For some reason credentials are missing`);
  //       throw new InternalServerError();
  //     }
  //     if (!credentials.email || !credentials.password) {
  //       throw new IncorrectEmailOrPasswordError();
  //     }
  //     const result = logInSchema.safeParse(credentials);
  //     if (!result.success) {
  //       throw new IncorrectEmailOrPasswordError();
  //     }
  //     const parsedCredentials = result.data;
  //     const normalizedEmail = parsedCredentials.email.toLowerCase();
  //     checkRateLimitAndThrowError(normalizedEmail);
  //     const user = await prisma.user.findUnique({
  //       where: { email: normalizedEmail },
  //       select: {
  //         id: true,
  //         password: true,
  //         email: true,
  //         emailVerified: true,
  //         name: true
  //       }
  //     });
  //     if (!user || !user.password || !user.email) {
  //       throw new IncorrectEmailOrPasswordError();
  //     }
  //     const isCorrectPassword = await verifyPassword(
  //       parsedCredentials.password,
  //       user.password
  //     );
  //     if (!isCorrectPassword) {
  //       throw new IncorrectEmailOrPasswordError();
  //     }
  //     if (!user.emailVerified || isBefore(new Date(), user.emailVerified)) {
  //       throw new UnverifiedEmailError();
  //     }
  //     return {
  //       id: user.id,
  //       email: user.email,
  //       name: user.name
  //     };
  //   }
  // })
  // CredentialsProvider({
  //   id: Provider.TotpCode,
  //   name: Provider.TotpCode,
  //   credentials: {
  //     token: { label: "Token", type: "text" },
  //     totpCode: { label: "TOTP code", type: "text" }
  //   },
  //   async authorize(credentials) {
  //     if (!credentials) {
  //       console.error(`For some reason credentials are missing`);
  //       throw new InternalServerError();
  //     }
  //     if (!credentials.totpCode) {
  //       throw new IncorrectTotpCodeError();
  //     }
  //     const result = submitTotpCodeSchema.safeParse(credentials);
  //     if (!result.success) {
  //       throw new IncorrectTotpCodeError();
  //     }
  //     const key = process.env.AUTH_SECRET;
  //     const parsedCredentials = result.data;
  //     const userId = symmetricDecrypt(parsedCredentials.token, key!);
  //     const expiry = new Date(symmetricDecrypt(parsedCredentials.expiry, key!));
  //     if (!isValid(expiry) || isBefore(expiry, new Date())) {
  //       throw new RequestExpiredError();
  //     }
  //     const user = await prisma.user.findUnique({
  //       where: { id: userId },
  //       select: {
  //         id: true,
  //         password: true,
  //         email: true,
  //         emailVerified: true,
  //         name: true,
  //         AuthenticatorApp: {
  //           select: {
  //             secret: true,
  //             recoveryCodes: true
  //           }
  //         }
  //       }
  //     });
  //     if (!user || !user.email) {
  //       throw new InternalServerError();
  //     }
  //     checkRateLimitAndThrowError(user.email);
  //     checkRateLimitAndThrowError(user.email);
  //     if (!user.AuthenticatorApp) {
  //       throw new InternalServerError();
  //     }
  //     const secret = symmetricDecrypt(user.AuthenticatorApp.secret, key!);
  //     if (secret.length !== 32) {
  //       console.error(
  //         `Authenticator app secret decryption failed. Expected key with length 32 but got ${secret.length}`
  //       );
  //       throw new InternalServerError();
  //     }
  //     const authenticator = new Authenticator({
  //       createDigest,
  //       createRandomBytes,
  //       keyDecoder,
  //       keyEncoder,
  //       window: [1, 0]
  //     });
  //     const isValidToken = authenticator.check(
  //       parsedCredentials.totpCode,
  //       secret
  //     );
  //     if (!isValidToken) {
  //       throw new IncorrectTotpCodeError();
  //     }
  //     return {
  //       id: user.id,
  //       email: user.email,
  //       name: user.name
  //     };
  //   }
  // }),
  // CredentialsProvider({
  //   id: Provider.RecoveryCode,
  //   name: Provider.RecoveryCode,
  //   credentials: {
  //     token: { label: "Token", type: "text" },
  //     recoveryCode: { label: "Recovery code", type: "text" }
  //   },
  //   async authorize(credentials) {
  //     if (!credentials) {
  //       console.error(`For some reason credentials are missing`);
  //       throw new InternalServerError();
  //     }
  //     if (!credentials.recoveryCode) {
  //       throw new IncorrectRecoveryCodeError();
  //     }
  //     const result = submitRecoveryCodeSchema.safeParse(credentials);
  //     if (!result.success) {
  //       throw new IncorrectRecoveryCodeError();
  //     }
  //     const key = process.env.AUTH_SECRET;
  //     const parsedCredentials = result.data;
  //     const userId = symmetricDecrypt(parsedCredentials.token, key!);
  //     const expiry = new Date(symmetricDecrypt(parsedCredentials.expiry, key!));
  //     if (!isValid(expiry) || isBefore(expiry, new Date())) {
  //       throw new RequestExpiredError();
  //     }
  //     const user = await prisma.user.findUnique({
  //       where: { id: userId },
  //       select: {
  //         id: true,
  //         password: true,
  //         email: true,
  //         emailVerified: true,
  //         name: true,
  //         AuthenticatorApp: {
  //           select: {
  //             recoveryCodes: true
  //           }
  //         }
  //       }
  //     });
  //     if (!user || !user.email) {
  //       throw new InternalServerError();
  //     }
  //     checkRateLimitAndThrowError(user.email);
  //     if (!user.AuthenticatorApp) {
  //       throw new InternalServerError();
  //     }
  //     if (!user.AuthenticatorApp.recoveryCodes) {
  //       throw new MissingRecoveryCodesError();
  //     }
  //     const recoveryCodes = JSON.parse(
  //       symmetricDecrypt(user.AuthenticatorApp.recoveryCodes, key!)
  //     );
  //     // Check if user-supplied code matches one
  //     const index = recoveryCodes.indexOf(
  //       parsedCredentials.recoveryCode.replaceAll("-", "")
  //     );
  //     if (index === -1) {
  //       throw new IncorrectRecoveryCodeError();
  //     }
  //     // Delete verified recoery code and re-encrypt remaining
  //     recoveryCodes[index] = null;
  //     await prisma.authenticatorApp.update({
  //       where: { userId: user.id },
  //       data: {
  //         recoveryCodes: symmetricEncrypt(JSON.stringify(recoveryCodes), key!)
  //       },
  //       select: {
  //         id: true // SELECT NONE
  //       }
  //     });
  //     return {
  //       id: user.id,
  //       email: user.email,
  //       name: user.name
  //     };
  //   }
  // })
  // CredentialsProvider({
  //   id: Provider.EmailVerification,
  //   name: Provider.EmailVerification,
  //   credentials: {
  //     token: { label: "Token", type: "text" }
  //   },
  //   async authorize(credentials) {
  //     if (!credentials?.token) {
  //       console.error("EmailVerification: No token provided");
  //       throw new InternalServerError();
  //     }
  //     const key = process.env.AUTH_SECRET!;
  //     try {
  //       console.log("EmailVerification: Attempting to decrypt token");
  //       const decoded = JSON.parse(
  //         symmetricDecrypt(String(credentials.token), key)
  //       );
  //       console.log("EmailVerification: Decoded token:", {
  //         userId: decoded.userId,
  //         email: decoded.email,
  //         purpose: decoded.purpose,
  //         expires: decoded.expires
  //       });
  //       // Validate the token
  //       if (
  //         !decoded.userId ||
  //         !decoded.email ||
  //         decoded.purpose !== "email-verification" ||
  //         !isValid(new Date(decoded.expires)) ||
  //         isBefore(new Date(decoded.expires), new Date())
  //       ) {
  //         console.error("EmailVerification: Token validation failed", {
  //           hasUserId: !!decoded.userId,
  //           hasEmail: !!decoded.email,
  //           correctPurpose: decoded.purpose === "email-verification",
  //           isValidDate: isValid(new Date(decoded.expires)),
  //           notExpired: !isBefore(new Date(decoded.expires), new Date())
  //         });
  //         throw new InternalServerError();
  //       }
  //       console.log("EmailVerification: Looking up user:", decoded.userId);
  //       const user = await prisma.user.findUnique({
  //         where: { id: decoded.userId },
  //         select: {
  //           id: true,
  //           email: true,
  //           name: true,
  //           emailVerified: true
  //         }
  //       });
  //       console.log("EmailVerification: User found:", {
  //         userExists: !!user,
  //         emailVerified: user?.emailVerified
  //       });
  //       if (!user || !user.emailVerified) {
  //         console.error(
  //           "EmailVerification: User not found or email not verified"
  //         );
  //         throw new InternalServerError();
  //       }
  //       console.log("EmailVerification: Authentication successful");
  //       return {
  //         id: user.id,
  //         email: user.email,
  //         name: user.name
  //       };
  //     } catch (error) {
  //       console.error("Error decoding verification token:", error);
  //       throw new InternalServerError();
  //     }
  //   }
  // })
] satisfies NextAuthConfig["providers"];
