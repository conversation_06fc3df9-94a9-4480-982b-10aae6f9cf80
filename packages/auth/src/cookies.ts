export const secure = new URL(getBaseUrl()).protocol === "https:";

export const AuthCookies = {
  CallbackUrl: secure ? "__Secure-authjs.callback-url" : "authjs.callback-url",
  CsrfToken: secure ? "__Host-authjs.csrf-token" : "authjs.csrf-token",
  SessionToken: secure
    ? "__Secure-authjs.session-token"
    : "authjs.session-token"
} as const;

export function getBaseUrl() {
  if (process.env.VERCEL_ENV === "production") {
    return "https://app.centaly.com";
  }

  if (process.env.VERCEL_URL) {
    return `https://${process.env.VERCEL_URL}`;
  }

  return "http://localhost:3000";
}

export const baseUrl = {
  Dashboard: getBaseUrl()
} as const;
