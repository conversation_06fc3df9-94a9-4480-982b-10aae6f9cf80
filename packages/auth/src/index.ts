import { cache } from "react";
import NextAuth, { type NextAuthConfig, type NextAuthResult } from "next-auth";
import { encode } from "next-auth/jwt";

import { adapter } from "./adapter";
import { callbacks } from "./callbacks";
import { events } from "./events";
import { providers } from "./providers";
import { session } from "./session";

export const authConfig: NextAuthConfig = {
  adapter,
  providers,
  secret: process.env.AUTH_SECRET,
  session,
  pages: {
    signIn: "/sign-in",
    signOut: "/sign-in",
    error: "/error",
    newUser: "/onboarding"
  },
  callbacks,
  events,
  jwt: {
    maxAge: session.maxAge,
    // Required line to encode credentials sessions
    async encode(arg) {
      return (arg.token?.sessionId as string) ?? encode(arg);
    }
  },
  trustHost: true
} satisfies NextAuthConfig;

// All those actions need to be called server-side
const result = NextAuth(authConfig);

export const handlers: NextAuthResult["handlers"] = result.handlers;
export const auth: NextAuthResult["auth"] = result.auth;
export const signIn: NextAuthResult["signIn"] = result.signIn;
export const signOut: NextAuthResult["signOut"] = result.signOut;

// Deduplicated server-side auth call
export const dedupedAuth = cache(auth);
