// import { cache } from "react";
// import { redirect } from "next/navigation";
// import { OnboardingStatus, Role } from "@repo/database";
// import { prisma } from "@repo/database/client";

// import { dedupedAuth, signOut } from "./index";
// import { getRedirectToSignIn } from "./redirect";
// import { checkSession } from "./session";

// const dedupedGetActiveOrganization = cache(async function () {
//   const session = await dedupedAuth();
//   if (!checkSession(session)) {
//     return redirect(getRedirectToSignIn());
//   }

//   const organization = await prisma.organization.findFirst({
//     where: {
//       memberships: {
//         some: {
//           userId: session.user.id
//         }
//       }
//     },
//     include: {
//       memberships: {
//         where: {
//           userId: session.user.id
//         },
//         select: {
//           organizationId: true,
//           userId: true,
//           role: true
//         }
//       }
//     },
//     take: 1
//   });

//   if (!organization) {
//     return redirect("/home");
//   }

//   return {
//     ...organization,
//     logo: organization.logo ? organization.logo : undefined
//   };
// });

// const dedupedGetUserInfo = cache(async function (userId: string) {
//   const userInfo = await prisma.user.findFirst({
//     where: {
//       id: userId
//     },
//     select: {
//       onboardingStatus: true,
//       organizationId: true,
//       memberships: {
//         select: {
//           role: true
//         },
//         take: 1
//       }
//     }
//   });

//   if (!userInfo) {
//     return signOut({ redirectTo: "/sign-in" });
//   }

//   // Extract membership data from the first membership if available
//   const role = userInfo.memberships[0]?.role;
//   const isOwner = userInfo.memberships[0]?.role === Role.owner;

//   return {
//     onboardingStatus: userInfo.onboardingStatus,
//     organizationId: userInfo.organizationId,
//     role,
//     isOwner
//   };
// });
// // To be used for auth routes
// export async function getAuthContextForAuth() {
//   const session = await dedupedAuth();
//   if (!checkSession(session)) {
//     return redirect(getRedirectToSignIn());
//   }

//   const userInfo = await dedupedGetUserInfo(session.user.id);

//   const enrichedSession = {
//     ...session,
//     user: {
//       ...session.user,
//       ...userInfo
//     }
//   };

//   return { session: enrichedSession };
// }

// // To be used for non-auth routes
// export async function getAuthContext() {
//   const { session } = await getAuthContextForAuth();

//   // Redirect to onboarding if not completed
//   if (session.user.onboardingStatus !== OnboardingStatus.complete) {
//     return redirect("/onboarding");
//   }

//   const userInfo = await dedupedGetUserInfo(session.user.id);

//   const enrichedSession = {
//     ...session,
//     user: {
//       ...session.user,
//       ...userInfo
//     }
//   };

//   return { session: enrichedSession };
// }

// export async function getAuthOrganizationContext() {
//   const session = await dedupedAuth();
//   if (!checkSession(session)) {
//     return redirect(getRedirectToSignIn());
//   }

//   const activeOrganization = await dedupedGetActiveOrganization();
//   const userInfo = await dedupedGetUserInfo(session.user.id);

//   if (!activeOrganization) {
//     return redirect("/home");
//   }

//   const enrichedSession = {
//     ...session,
//     user: {
//       ...session.user,
//       ...userInfo
//     }
//   };

//   return { session: enrichedSession, organization: activeOrganization };
// }
