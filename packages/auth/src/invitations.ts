import { InvitationStatus, Role } from "@repo/database";
import { prisma } from "@repo/database/client";
import { sendInvitationEmail } from "@repo/email/senders/send-invitation-email";

export async function checkIfCanInvite(
  email: string,
  organizationId: string
): Promise<boolean> {
  const [countMemberships, countInvitations] = await prisma.$transaction([
    prisma.membership.count({
      where: {
        user: { email },
        organizationId
      }
    }),
    prisma.invitation.count({
      where: {
        email: email,
        organizationId: organizationId,
        status: InvitationStatus.pending
      }
    })
  ]);

  return countMemberships === 0 && countInvitations === 0;
}

export async function createInvitation(
  email: string,
  role: Role,
  organizationId: string,
  invitedByUserId: string
) {
  const result = await prisma.$transaction([
    // revoke old invitations
    prisma.invitation.updateMany({
      where: {
        organizationId,
        email,
        status: InvitationStatus.pending
      },
      data: {
        status: InvitationStatus.revoked
      }
    }),
    // create new invitation
    prisma.invitation.create({
      data: {
        email,
        role,
        organizationId,
        lastSentAt: new Date(),
        invitedByUserId: invitedByUserId
      },
      select: {
        id: true,
        role: true,
        email: true
        // token: true
      }
    })
  ]);

  return result[1];
}

type SendInvitationParams = {
  email: string;
  organizationName: string;
  invitedByEmail: string;
  invitedByName: string;
  token: string;
  invitationId: string;
  organizationId: string;
};

export async function sendInvitationRequest({
  email,
  organizationName,
  invitedByName,
  token,
  invitationId,
  organizationId
}: SendInvitationParams): Promise<void> {
  await sendInvitationEmail({
    recipient: email,
    organizationName,
    invitedByName,
    inviteLink: `${"/invite/join"}/${token}`
  });
  await prisma.invitation.update({
    where: {
      id: invitationId,
      organizationId
    },
    data: { lastSentAt: new Date() },
    select: { id: true }
  });
}
