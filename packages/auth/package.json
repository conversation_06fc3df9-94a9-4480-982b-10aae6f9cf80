{"name": "@repo/auth", "private": true, "version": "0.0.0", "type": "module", "packageManager": "pnpm@9.15.4", "exports": {"./keys": "./keys.ts", "./adapter": "./src/adapter.ts", "./callbacks": "./src/callbacks.ts", "./constants": "./src/constants.ts", "./context": "./src/context.ts", "./cookies": "./src/cookies.ts", "./encryption": "./src/encryption.ts", "./errors": "./src/errors.ts", "./events": "./src/events.ts", ".": "./src/index.ts", "./invitations": "./src/invitations.ts", "./password": "./src/password.ts", "./permissions": "./src/permissions.ts", "./providers": "./src/providers.ts", "./providers.types": "./src/providers.types.ts", "./redirect": "./src/redirect.ts", "./schemas": "./src/schemas.ts", "./session": "./src/session.ts", "./verification": "./src/verification.ts"}, "scripts": {"lint": "eslint .", "lint:fix": "eslint --fix .", "typecheck": "tsc --noEmit"}, "dependencies": {"@auth/prisma-adapter": "2.7.4", "@otplib/core": "^12.0.1", "@otplib/plugin-crypto": "12.0.1", "@otplib/plugin-thirty-two": "12.0.1", "@repo/database": "workspace:*", "@repo/email": "workspace:*", "@t3-oss/env-nextjs": "0.13.6", "bcryptjs": "3.0.2", "date-fns": "4.1.0", "memory-cache": "^0.2.0", "next": "^15.4.4", "next-auth": "5.0.0-beta.25", "otplib": "12.0.1", "react": "^19.1.1", "react-dom": "^19.1.1", "ulid": "^3.0.1", "uuid": "11.1.0", "zod": "3.25.56"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/prettier-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/bcryptjs": "2.4.6", "@types/memory-cache": "0.2.6", "@types/node": "^22.15.3", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@types/uuid": "^10.0.0"}, "prettier": "@repo/prettier-config"}