// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider        = "prisma-client-js"
  output          = "../generated/prisma"
  previewFeatures = ["driverAdapters"]
  binaryTargets   = ["native", "rhel-openssl-3.0.x"]
}

datasource db {
  provider     = "mysql"
  url          = env("DATABASE_URL")
  relationMode = "prisma"
}

enum Role {
  viewer      @map("viewer")
  contributor @map("contributor")
  admin       @map("admin")
  owner       @map("owner")
}

enum OnboardingStatus {
  incomplete @map("incomplete")
  workspace  @map("workspace")
  invite     @map("invite")
  complete   @map("complete")
}

enum InvitationStatus {
  pending  @map("pending")
  accepted @map("accepted")
  rejected @map("rejected")
  revoked  @map("revoked")
  expired  @map("expired")
}

enum MembershipStatus {
  active      @map("active")
  deactivated @map("deactivated")
  banned      @map("banned")
}

enum ActorType {
  system @map("system")
  member @map("member")
  api    @map("api")
}

model User {
  id                 String               @id @default(ulid())
  image              String?
  name               String
  email              String?              @unique @db.VarChar(255)
  emailVerified      DateTime?
  password           String?              @db.VarChar(60)
  createdAt          DateTime
  updatedAt          DateTime
  onboardingStatus   OnboardingStatus     @default(incomplete)
  defaultWorkspace   String?
  organizationId     String?
  role               String               @default("user")
  banned             Boolean              @default(false)
  banReason          String?
  banExpires         DateTime?
  lastLogin          DateTime?
  accounts           Account[]
  sessions           Session[]
  memberships        Membership[]
  todos              Todo[]
  ChangeEmailRequest ChangeEmailRequest[]
  organization       Organization?        @relation(fields: [organizationId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  AuthenticatorApp   AuthenticatorApp?
  sentInvitations    Invitation[]         @relation("InvitedByUser")

  @@index([name], map: "IX_User_name")
  @@index([organizationId])
}

model Session {
  id                   String   @id @default(ulid())
  sessionToken         String   @unique
  userId               String
  expires              DateTime
  createdAt            DateTime @default(now())
  updatedAt            DateTime @updatedAt
  ipAddress            String?
  userAgent            String?
  activeOrganizationId String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([activeOrganizationId])
}

model VerificationToken {
  id         String   @id @default(ulid())
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model Account {
  id                String   @id @default(ulid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @default(now()) @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  @@unique([provider, providerAccountId])
  @@index([userId], map: "IX_Account_userId")
}

model ResetPasswordRequest {
  id        String   @id @default(ulid())
  email     String
  expires   DateTime
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([email])
}

model ChangeEmailRequest {
  id        String   @id @default(ulid())
  userId    String
  email     String
  expires   DateTime
  valid     Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  @@index([userId])
}

model Invitation {
  id              String           @id @default(ulid())
  organizationId  String
  token           String           @default(ulid())
  email           String           @db.VarChar(255)
  role            Role             @default(contributor)
  status          InvitationStatus @default(pending)
  invitedByUserId String
  lastSentAt      DateTime?
  createdAt       DateTime         @default(now())
  updatedAt       DateTime         @default(now()) @updatedAt
  Organization    Organization     @relation(fields: [organizationId], references: [id])
  invitedByUser   User             @relation("InvitedByUser", fields: [invitedByUserId], references: [id])

  @@index([organizationId])
  @@index([token])
  @@index([email])
  @@index([status])
  @@index([invitedByUserId])
}

model Organization {
  id       String  @id @default(ulid())
  name     String
  slug     String  @unique @db.VarChar(255)
  logo     String?
  metadata String?

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt

  todos Todo[]
  users User[]

  memberships Membership[]
  invitations Invitation[]

  @@index([createdAt])
  @@index([name])
}

model Membership {
  id               String           @id @default(ulid())
  organizationId   String
  userId           String
  role             Role             @default(contributor)
  membershipStatus MembershipStatus @default(active)
  createdAt        DateTime         @default(now())
  updatedAt        DateTime         @default(now()) @updatedAt
  // Relations
  user             User             @relation(fields: [userId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  organization     Organization     @relation(fields: [organizationId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  @@unique([organizationId, userId])
  @@index([userId])
  @@index([organizationId])
}

model AuthenticatorApp {
  id            String   @id @default(ulid())
  userId        String   @unique
  accountName   String   @db.VarChar(255)
  issuer        String   @db.VarChar(255)
  secret        String   @db.VarChar(255)
  recoveryCodes String   @db.VarChar(1024)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  @@index([userId])
}

model Todo {
  id             Int          @id @default(autoincrement())
  text           String
  completed      Boolean      @default(false)
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  userId         String
  user           User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([organizationId])
  @@index([userId, organizationId])
}
