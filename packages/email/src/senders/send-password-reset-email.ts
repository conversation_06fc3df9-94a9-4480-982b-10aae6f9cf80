import { render } from "@react-email/render";

import { sendEmail } from "../resend/send-email";
import {
  ResetPasswordEmail,
  ResetPasswordEmailProps
} from "../templates/reset-password-email";

export async function sendPasswordResetEmail(
  input: ResetPasswordEmailProps & { recipient: string }
): Promise<void> {
  const component = ResetPasswordEmail(input);
  const html = await render(component);
  const text = await render(component, { plainText: true });

  await sendEmail({
    recipient: input.recipient,
    subject: "Reset password instructions",
    html,
    text
  });
}
