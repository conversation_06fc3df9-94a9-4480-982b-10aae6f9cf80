import { render } from "@react-email/render";
import {
  WelcomeEmail,
  type WelcomeEmailProps,
} from "../templates/welcome-email";

import { sendEmail } from "../resend/send-email";

export async function sendWelcomeEmail(
  input: WelcomeEmailProps & { recipient: string }
): Promise<void> {
  const component = WelcomeEmail(input);
  const html = await render(component);
  const text = await render(component, { plainText: true });

  await sendEmail({
    recipient: input.recipient,
    subject: "Welcome to Centaly",
    html,
    text,
  });
}
