{"name": "@repo/email", "version": "0.0.0", "type": "module", "packageManager": "pnpm@9.15.4", "exports": {"./resend": "./src/resend.ts", "./resend-mailer": "./src/resend/index.ts", "./senders/*": "./src/senders/*.ts", "./senders/send-invitation-email": "./src/senders/send-invitation-email.ts", "./senders/send-welcome-email": "./src/senders/send-welcome-email.ts", "./senders/send-connected-account-security-alert-email": "./src/senders/send-connected-account-security-alert-email.ts", "./templates": "./src/templates/*", "./invite-user-email": "./src/templates/invite-user-email.tsx", "./reset-password-email": "./src/templates/reset-password-email.tsx", "./verify-email": "./src/templates/verify-email.tsx", "./confirm-email-address-change-email": "./src/confirm-email-address-change-email.tsx", "./connected-account-security-alert-email": "./src/connected-account-security-alert-email.tsx", "./feedback-email": "./src/feedback-email.tsx", "./invitation-email": "./src/invitation-email.tsx", "./password-reset-email": "./src/password-reset-email.tsx", "./revoked-invitation-email": "./src/revoked-invitation-email.tsx", "./welcome-email": "./src/welcome-email.tsx", "./send-verify-email-address-email": "./src/senders/send-verify-email-address-email.ts", "./send-reset-password-email": "./src/senders/send-reset-password-email.ts", "./send-invitation-email": "./src/senders/send-invitation-email.ts"}, "scripts": {"lint": "eslint .", "typecheck": "tsc --noEmit"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@repo/prettier-config": "workspace:*", "@types/node": "^22.15.3", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2"}, "dependencies": {"@react-email/components": "^0.0.32", "@react-email/render": "^1.0.4", "@react-email/tailwind": "^1.0.4", "react": "^19.1.1", "react-dom": "^19.1.1", "react-email": "^3.0.6", "resend": "^4.6.0", "zod": "^3.25.76"}, "prettier": "@repo/prettier-config"}