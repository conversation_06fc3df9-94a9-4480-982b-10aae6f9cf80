{"name": "@repo/ui", "version": "0.0.0", "private": true, "packageManager": "pnpm@9.15.4", "exports": {"./globals.css": "./src/styles/globals.css", "./postcss.config": "./postcss.config.mjs", "./lib/*": "./src/lib/*.ts", "./components/*": "./src/components/*.tsx", "./hooks/*": "./src/hooks/*.ts"}, "scripts": {"lint": "eslint .", "typecheck": "tsc --noEmit"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/prettier-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@tailwindcss/cli": "^4.1.11", "@types/node": "^22.15.3", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "eslint": "^9.30.0", "tailwindcss": "^4.1.11", "tw-animate-css": "^1.3.5", "typescript": "5.8.2"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "input-otp": "^1.4.2", "lucide-react": "^0.525.0", "next-themes": "^0.4.6", "radix-ui": "^1.4.2", "react": "^19.1.1", "react-day-picker": "^9.8.0", "react-dom": "^19.1.1", "react-hook-form": "^7.60.0", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "zod": "^3.25.76"}, "prettier": "@repo/prettier-config"}