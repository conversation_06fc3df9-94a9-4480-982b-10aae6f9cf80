# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Structure

This is a Turborepo monorepo with the following structure:

- `apps/dashboard/` - Next.js application (main app)
- `apps/backend/` - Hono.js backend API server
- `apps/email/` - Email preview development server using React Email
- `packages/ui/` - Shared React component library using shadcn/ui
- `packages/database/` - Shared Prisma database package with MySQL (PlanetScale)
- `packages/auth/` - Shared authentication package using Auth.js v5
- `packages/email/` - Shared email package with React Email templates and Resend integration
- `packages/eslint-config/` - Shared ESLint configurations
- `packages/typescript-config/` - Shared TypeScript configurations
- `packages/prettier-config/` - Shared Prettier configuration

## Essential Commands

### Development

```bash
# Start development server for all apps
pnpm dev

# Start development with clean ports (kills existing processes)
pnpm dev:clean

# Kill processes on development ports
pnpm kill-ports

# Start development for specific app
pnpm dev --filter=dashboard
pnpm dev --filter=backend
pnpm dev --filter=email

# Start UI component development
cd packages/ui && pnpm dev:components && pnpm dev:styles
```

### Building

```bash
# Build all packages and apps
pnpm build

# Build specific app
pnpm build --filter=dashboard
```

### Code Quality

```bash
# Lint all packages
pnpm lint

# Type check all packages
pnpm typecheck

# Format code
pnpm format
```

### Database

```bash
# Generate Prisma client
pnpm db:generate

# Open Prisma Studio (development)
pnpm db:studio

# Open Prisma Studio (production)
pnpm db:studio-prod

# Run database migrations (from database package)
cd packages/database && pnpm db:deploy

# Seed database (from database package)
cd packages/database && pnpm db:seed

# Push schema changes (dev only, from database package)
cd packages/database && pnpm db:push
```

### Deployment

```bash
# Deploy to staging
pnpm deploy:staging

# Deploy to production
pnpm deploy:production
```

### Testing

The project uses Turbo's task orchestration. Check individual package.json files for specific test commands.

## Architecture Notes

### UI Package (`packages/ui/`)

- Uses shadcn/ui components with Tailwind CSS v4
- Components are exported from `src/components/`
- Utilities (like `cn` function) are in `src/utils/`
- Global styles are in `src/styles/globals.css`
- Component generation: `pnpm generate:component`

### Database Package (`packages/database/`)

- Prisma ORM with MySQL (PlanetScale)
- Shared database client and types
- Migrations managed via Prisma
- Exports client from `@repo/database`
- Prisma Accelerate extension for connection pooling

### Dashboard App (`apps/dashboard/`)

- Next.js 15 with React 19
- Uses Turbopack for development
- Imports UI components from `@repo/ui`
- Tailwind CSS v4 with PostCSS

### backend App (`apps/backend/`)

- Hono.js backend server
- OpenAPI/Swagger documentation
- Type-safe environment variables with Zod
- Error handling and request logging middleware
- CORS configured for dashboard app
- Authentication integration

### Email App (`apps/email/`)

- React Email preview development server
- Runs on port 3003 for email template development
- Uses templates from `@repo/email` package
- Email export functionality for static HTML

### Email Package (`packages/email/`)

- React Email templates and components
- Resend integration for email sending
- Exports templates from `@repo/email/templates`
- Exports email senders from `@repo/email/senders`
- Exports Resend configuration from `@repo/email/resend`
- TypeScript definitions for email sending

### Prettier Config Package (`packages/prettier-config/`)

- Shared Prettier configuration
- Import sorting with `@ianvs/prettier-plugin-sort-imports`
- Tailwind CSS class sorting with `prettier-plugin-tailwindcss`
- Exported as `@repo/prettier-config`

### Shared Configurations

- ESLint: Shared configs for base, Next.js, and React
- TypeScript: Shared tsconfig.json files
- Tailwind: Shared configuration and styles
- Prettier: Shared formatting configuration

## Development Workflow

1. The monorepo uses pnpm workspaces
2. All packages are linked via `workspace:*` protocol
3. Turbo handles task orchestration and caching
4. TypeScript is used throughout with strict type checking
5. ESLint enforces code quality with max-warnings 0

## Package Dependencies

The UI package uses:

- Radix UI primitives
- Class Variance Authority for component variants
- Tailwind Merge and clsx for className utilities
- Tailwind CSS v4 with CLI

The Database package uses:

- Prisma ORM for database operations
- MySQL (PlanetScale) as the database
- Prisma Accelerate for connection pooling
- TypeScript for type safety

The Email package uses:

- React Email for email template development
- Resend for email delivery service
- Tailwind CSS for email styling
- Zod for type validation
- TypeScript for type safety

The Prettier Config package uses:

- Prettier for code formatting
- Import sorting plugin for organized imports
- Tailwind CSS plugin for class sorting
- Shared configuration across monorepo

## Node.js and Package Manager Requirements

- Node.js >= 20
- pnpm 9.15.4 (specified in packageManager field)
- TypeScript 5.8.x across all packages

## Port Management

The project includes utilities for managing development server ports:

```bash
# Kill processes on development ports (3000, 3001)
pnpm kill-ports

# Start development with clean ports
pnpm dev:clean

# Manual port management
lsof -ti:3001                    # Find process using port 3001
kill -9 $(lsof -ti:3001)        # Kill process on port 3001
```

## Git Commit Guidelines

This project uses Husky with commitlint to enforce standardized commit messages and quality checks.

### Commit Message Format

All commit messages must follow the Conventional Commits specification:

```
type(scope): description

[optional body]

[optional footer(s)]
```

**Allowed commit types:**

- `feat`: new feature
- `fix`: bug fix
- `docs`: documentation changes
- `style`: formatting, missing semicolons, etc.
- `refactor`: refactoring production code
- `test`: adding tests, refactoring test code
- `chore`: updating build tasks, package manager configs, etc.
- `perf`: performance improvements
- `ci`: continuous integration related
- `build`: build system or external dependencies
- `revert`: reverting a previous commit

**Examples:**

```bash
feat(auth): add user registration endpoint
fix(ui): resolve button hover state issue
docs: update API documentation
chore: upgrade dependencies
```

### Pre-commit Hooks

Before each commit, the following checks run automatically:

1. **Linting**: `pnpm lint` - ensures code quality across all packages
2. **Type checking**: `pnpm typecheck` - validates TypeScript types

If either check fails, the commit will be rejected. Fix the issues and try again.

### Commit Message Validation

Enforced by commitlint configuration:

- No emojis allowed in commit messages
- Subject must be between 3-72 characters
- Header (including scope) cannot exceed 100 characters
- Must follow conventional commit format
- Subject cannot be empty

### Manual Quality Checks

Run these commands before committing:

```bash
# Run all quality checks
pnpm lint && pnpm typecheck

# Format code
pnpm format
```

## Codebase-Specific Rules and Best Practices

### General Principles

- Code should be easy to understand. Use descriptive variable and function names.
- Group related functionality together naturally. Avoid both overly broad modules and artificial micro-separations that make code harder to work with.
- Create abstractions (e.g., Higher-Order Components, custom hooks) only when they are reused across multiple components or solve a specific, recurring problem.

### Frontend Application (`apps/dashboard/`)

#### File Organization and Naming Conventions

- Use **kebab-case** for file and directory names (e.g., `sign-in-form.tsx`, `workspace-context.tsx`)
- Use **camelCase** for functions, variables, and hooks (e.g., `useOrganizations`, `onSubmit`)
- Features should be organized in `src/features/[feature-name]/` directories with subdirectories:
  - `components/` - React components specific to the feature
  - `hooks/` - Custom React hooks
  - `contexts/` - React contexts and providers
  - `schemas/` - Zod validation schemas
  - `dal/` - Data access layer functions

#### Component Architecture

- **Functional Components Only**: Use React functional components with hooks
- **Component Structure**: Follow this order in components:
  1. Imports (external libraries first, then internal)
  2. Type definitions and interfaces
  3. Component function declaration
  4. State declarations (useState, useForm, etc.)
  5. Effect hooks (useEffect, etc.)
  6. Event handlers
  7. Render logic
  8. Export statement

#### State Management

- Use **React Hook Form** with **Zod** for form validation
- Use **Zustand** for global state management
- Use **TanStack Query** for server state management

#### Schema and Type Definitions

- Define Zod schemas in separate files (e.g., `sign-in-schema.ts`)
- Export both schema and inferred types: `export type SignInFormData = z.infer<typeof signInSchema>`
- Use TypeScript interfaces for complex object structures
- Prefer type inference over explicit typing where possible

#### UI Components and Styling

- Use components from `@repo/ui` package exclusively
- Import UI components individually: `import { Button } from "@repo/ui/components/button"`
- Use Tailwind CSS classes for styling
- Use the `cn()` utility from `@repo/ui/utils/cn` for conditional classes
- Avoid inline styles - use Tailwind classes

#### Error Handling and Loading States

- Always handle loading states with boolean flags (e.g., `isLoading`)
- Use error state variables (e.g., `error: string`)
- Clear errors when retrying operations
- Show user-friendly error messages
- Use try-catch blocks in async functions

### Backend Application (`apps/backend/`)

#### File Organization and Naming Conventions

- Use **kebab-case** for file names (e.g., `error-handler.ts`, `secure-headers.ts`)
- Use **camelCase** for function and variable names
- Use **PascalCase** for class names (e.g., `APIError`)
- Organize routes in separate files by resource (e.g., `users.ts`, `organizations.ts`)

#### API Route Structure

- Use **OpenAPI/Hono** with Zod for all API routes
- Define routes using `createRoute` with full OpenAPI specification
- Include comprehensive request/response schemas
- Use consistent HTTP status codes (200, 201, 400, 401, 404, 409, 500)
- Group related endpoints in router files

#### Schema Definitions

- Define Zod schemas for all request/response validation
- Use descriptive schema names (e.g., `userSchema`, `createUserRoute`)
- Include proper validation rules (e.g., `.uuid()`, `.email()`, `.min(8)`)
- Reuse common schemas from `schemas` object in `lib/openapi.ts`

#### Error Handling

- Use the custom `APIError` class for throwing application errors
- Use helper functions: `notFound()`, `badRequest()`, `unauthorized()`, etc.
- Handle Prisma errors specifically (P2002 for conflicts, P2025 for not found)
- Include timestamps in all error responses
- Only expose error details in development mode

#### Database Integration

- Import Prisma client from `@repo/database/client`
- Use proper TypeScript types from generated Prisma client
- Handle database errors appropriately
- Use transactions for multi-step operations
- Follow Prisma best practices for queries

#### Middleware Pattern

- Apply middleware in correct order: request-id → errors → logger → CORS → security headers
- Use type-safe middleware with proper Context typing
- Keep middleware focused on single responsibility
- Use environment variables for configuration

#### Response Formatting

- Always include timestamps in responses
- Use consistent JSON structure across endpoints
- Include request IDs in error responses
- Use proper HTTP status codes
- Format dates as ISO strings

#### Environment and Configuration

- Use Zod for environment variable validation in `lib/env.ts`
- Never expose sensitive configuration in responses
- Use different configs for development/production
- Validate all environment variables at startup

### Shared Conventions

#### Code Quality

- Use TypeScript strict mode across all packages
- Prefer explicit typing for function parameters and return types
- Use meaningful variable and function names
- Keep functions small and focused on single responsibility
- Write self-documenting code with clear naming

#### Import/Export Patterns

- DO NOT use barrel exports in index files

#### Documentation

- Write comments to explain complex logic, non-obvious decisions, or workarounds. Avoid commenting on obvious code.
- Use JSDoc blocks to describe purpose, parameters, and return values.
- Comments should explain why not just what.
- Include parameter descriptions for public APIs
- Document expected behavior and edge cases
