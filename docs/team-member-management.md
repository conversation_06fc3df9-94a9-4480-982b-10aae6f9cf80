# Team Member Management Feature

## Overview

The Team Member Management feature enables workspace administrators to invite new members, view all team members (active, suspended, and invited), and manage their access levels within the organization. This feature uses Auth.js v5 for session management and custom API endpoints to provide comprehensive member management capabilities including invitation workflows, status tracking, and role-based access control.

Main Objectives:

- Enable workspace managers to invite new members via email with role assignment
- Display a comprehensive table of all members with filtering and search capabilities
- Track member status (active, suspended, invited) across multiple data sources
- Provide resend invitation functionality for pending invitees
- Provide ban/unban functionality for user management
- Seamless integration with Auth.js v5 authentication system

## Implementation Approach

Three-Layer Architecture:

1. Backend API Layer: Create organization member management endpoints
2. DAL (Data Access Layer): Server-side data fetching with caching
3. Client Layer: React Query hooks for client-side state management

Data Aggregation Strategy:
The deriveMemberStatus function requires combining data from three sources:

- Membership table: role fields and organization relationships
- Invitation table: status field and invitation tracking
- User table: name, email, emailVerified, banned, banReason, banExpires fields

## 1. Architecture Overview

### System Design

The Team Member Management feature follows a **three-layer architecture** that separates concerns and promotes maintainability:

```
┌─────────────────────────────────────────────────────────────┐
│                    Frontend Layer                           │
├─────────────────────────────────────────────────────────────┤
│  React Components (settings-members/)                       │
│  • team-members-table.tsx (AG Grid table)                   │
│  • team-member-invite-modal.tsx (invitation form)           │
│  • team-members-table-filter.tsx (search & filters)         │
├─────────────────────────────────────────────────────────────┤
│  State Management & Data Layer                              │
│  • hooks/ (React Query + custom hooks)                      │
│  • dal/ (Server-side data access)                           │
│  • types/ & schemas/ (Type definitions)                     │
├─────────────────────────────────────────────────────────────┤
│                    Backend Layer                            │
│  • /api/v1/organizations/{slug}/members (REST API)          │
│  • /api/v1/organizations/{slug}/invitations (REST API)      │
│  • Prisma ORM + PlanetScale MySQL                           │
│  • Auth.js v5 session management                            │
│  • Custom invitation and email handling                     │
└─────────────────────────────────────────────────────────────┘
```

### File Hierarchy

```
apps/dashboard/src/features/settings-members/
├── dal/
│   └── members.ts                    # Server-side data fetching (SSR)
├── hooks/
│   ├── use-members.ts               # React Query hooks & mutations
│   └── use-team-members-filter.ts   # AG Grid filtering logic
├── schemas/
│   └── member-schema.ts             # Zod validation schemas
├── types/
│   └── member-types.ts              # TypeScript type definitions
├── team-member-invite-modal.tsx     # Invitation modal component
├── team-members-table-filter.tsx    # Search & filter controls
└── team-members-table.tsx           # Main AG Grid table component

apps/backend/src/routes/
└── members.ts                       # REST API endpoints

apps/dashboard/src/lib/api/
└── members.ts                       # Client-side API functions
```

## 2. Key Components

### Core UI Components

**`team-members-table.tsx`** - Main data table component

- **Responsibility**: Displays members in AG Grid with custom cell renderers
- **Key Features**: Status badges, role management, action dropdowns, pagination, resend invitation functionality
- **Dependencies**: AG Grid Community, custom hooks, UI components
- **Line Reference**: `apps/dashboard/src/features/settings-members/team-members-table.tsx:199`

**`team-member-invite-modal.tsx`** - Invitation dialog

- **Responsibility**: Handles member invitation with email tags and role selection
- **Key Features**: Multi-email input, role selection (viewer, contributor, admin), custom API integration
- **Dependencies**: React Hook Form, Zod validation, custom API client
- **Line Reference**: `apps/dashboard/src/features/settings-members/team-member-invite-modal.tsx:48`

**`team-members-table-filter.tsx`** - Search and filter controls

- **Responsibility**: Provides search input and status filtering popover
- **Key Features**: Real-time search, multi-select status filters, filter state management
- **Dependencies**: Custom filter hook, UI components
- **Line Reference**: `apps/dashboard/src/features/settings-members/team-members-table-filter.tsx:36`

### Data Management Layer

**`dal/members.ts`** - Server-side data access

- **Responsibility**: Cached server-side data fetching for SSR/SSG
- **Key Features**: React cache(), headers forwarding, error handling
- **Dependencies**: Next.js headers, fetch API
- **Line Reference**: `apps/dashboard/src/features/settings-members/dal/members.ts:10`

**`hooks/use-members.ts`** - Client-side state management

- **Responsibility**: React Query hooks with optimistic updates
- **Key Features**: Data fetching, mutations, optimistic UI updates, cache invalidation, resend invitation functionality, ban/unban operations
- **Dependencies**: TanStack Query, API functions, toast notifications
- **Line Reference**: `apps/dashboard/src/features/settings-members/hooks/use-members.ts:16`

**`hooks/use-team-members-filter.ts`** - Filter state management

- **Responsibility**: Manages AG Grid quick filter and external filter state
- **Key Features**: Search text management, status filtering, grid integration
- **Dependencies**: AG Grid React, React hooks
- **Line Reference**: `apps/dashboard/src/features/settings-members/hooks/use-team-members-filter.ts:20`

### Backend API

**`apps/backend/src/routes/members.ts`** - REST API endpoints

- **Responsibility**: CRUD operations for organization members and invitations
- **Key Features**: Member listing with aggregation, role updates, ban/unban operations, removal, invitation creation, resend invitations
- **Dependencies**: Hono.js, Prisma ORM, OpenAPI integration, email sending
- **Line Reference**: `apps/backend/src/routes/members.ts:104`

**`apps/dashboard/src/lib/api/members.ts`** - API client functions

- **Responsibility**: Type-safe API calls from frontend to backend
- **Key Features**: Standardized error handling, TypeScript interfaces, invitation management, ban/unban operations
- **Dependencies**: Custom API configuration, fetch wrapper
- **Line Reference**: `apps/dashboard/src/lib/api/members.ts:54`

### Authentication & Session Management

**`apps/dashboard/src/features/auth/hooks/use-current-user.ts`** - Current user session hook

- **Responsibility**: Provides client-side access to current user session data
- **Key Features**: React Query integration, Auth.js v5 session validation, caching, error handling
- **Dependencies**: TanStack Query, custom API client
- **Line Reference**: `apps/dashboard/src/features/auth/hooks/use-current-user.ts:30`

**`packages/auth/src/index.ts`** - Auth.js v5 configuration

- **Responsibility**: Configures Auth.js v5 with database sessions and providers
- **Key Features**: Database session strategy, custom callbacks, email verification
- **Dependencies**: Auth.js v5, Prisma adapter, custom providers
- **Line Reference**: `packages/auth/src/index.ts:11`

## 3. Data Flow

### Member Data Aggregation

```mermaid
graph TD
    A[Frontend Request] --> B[Backend API]
    B --> C[Prisma Query: Members Table]
    B --> D[Prisma Query: Invitations Table]
    B --> E[Prisma Query: Users Table]
    C --> F[Data Aggregation Layer]
    D --> F
    E --> F
    F --> G[deriveMemberStatus Function]
    G --> H[Formatted Response]
    H --> I[React Query Cache]
    I --> J[AG Grid Table]
```

### Status Derivation Logic

The `deriveMemberStatus` function (apps/backend/src/routes/members.ts:242) combines data from three sources:

1. **Priority 1 - Banned User**: If `user.banned === true` → `"suspended"`
2. **Priority 2 - Pending Invitation**: If `invitation.status === "pending"` → `"pending"`
3. **Priority 3 - Active User**: If invitation accepted + email verified → `"active"`
4. **Default**: Legacy members without invitations → `"active"`

### Invitation Flow

```mermaid
sequenceDiagram
    participant U as User
    participant M as Modal
    participant API as Backend API
    participant DB as Database
    participant EMAIL as Email Service
    participant INVITEE as Invitee

    U->>M: Enter emails & role
    M->>API: POST /api/v1/organizations/{slug}/invitations
    API->>DB: Create invitation records
    API->>EMAIL: Send invitation emails
    EMAIL->>INVITEE: Email delivered to invitees
    API->>M: Return invitation results
    M->>API: Trigger cache invalidation
    API->>U: Update table with new pending members
```

### Resend Invitation Flow

```mermaid
sequenceDiagram
    participant U as User
    participant T as Table
    participant API as Backend API
    participant DB as Database
    participant EMAIL as Email Service
    participant INVITEE as Invitee

    U->>T: Click "Resend invite" dropdown action
    T->>API: POST /api/v1/organizations/{slug}/invitations/{id}/resend
    API->>DB: Update invitation lastSentAt timestamp
    API->>EMAIL: Send invitation email
    EMAIL->>INVITEE: Email re-delivered to invitee
    API->>T: Return success/error response
    T->>API: Trigger cache invalidation
    API->>U: Show success/error toast notification
```

### Data Update Patterns

- **Optimistic Updates**: Immediate UI updates before server confirmation
- **Cache Invalidation**: React Query invalidates affected queries after mutations
- **Error Rollback**: Failed mutations revert optimistic changes
- **Real-time Sync**: Status changes reflect immediately in the table

## 4. Patterns

### State Management Architecture

- **React Query**: Server state management with caching and synchronization
- **Local State**: Component-level state for UI interactions (modals, filters)
- **Form State**: React Hook Form with Zod validation for data integrity
- **Session State**: Auth.js v5 session management with custom hooks

### Component Composition

- **Container/Presentational Pattern**: Hooks handle logic, components handle rendering
- **Custom Hooks Pattern**: Reusable logic encapsulated in custom hooks
- **Render Props Pattern**: AG Grid cell renderers for customized table cells
- **Compound Components**: Modal components with sub-components for structure

### API Integration Patterns

- **Server-Side Rendering**: DAL functions provide initial data for SSR
- **Client-Side Hydration**: React Query manages subsequent data fetching
- **Type-Safe API Calls**: Shared TypeScript interfaces between frontend/backend
- **Error Boundary Pattern**: Consistent error handling across API layers

### Authentication Integration

- **Auth.js v5**: Database session strategy with custom session validation
- **Session Management**: Server-side session validation for API access via middleware
- **Role-Based Access**: Permission checks at both API and UI levels
- **Current User Hook**: `useCurrentUser` hook provides client-side session access
- **Optimistic Auth Updates**: Immediate UI feedback for auth-related actions

### Member Management Operations

#### Resend Email Invitation

- **Custom API**: Uses `POST /api/v1/organizations/{slug}/invitations/{id}/resend` endpoint
- **User Feedback**: Toast notifications for loading, success, and error states
- **State Management**: React Query mutation hooks for optimistic updates and cache invalidation
- **Error Handling**: Comprehensive error handling with user-friendly messages

#### Ban/Unban Users

- **Custom API**: Uses `PATCH /api/v1/organizations/{slug}/members/{id}` with `ban: boolean` parameter
- **Database Integration**: Updates `User.banned`, `User.banReason`, and `User.banExpires` fields
- **UI Integration**: Immediate status updates in the members table
- **Permission Checks**: Only admins and owners can ban/unban users

#### Role Management

- **Role Types**: Supports `viewer`, `contributor`, `admin`, and `owner` roles
- **Database Updates**: Updates both `Membership.role` and `User.role` fields
- **Permission Mapping**: Maps organization roles to system-wide user roles
- **Optimistic Updates**: Immediate UI feedback with rollback on errors

### Email Integration Architecture

- **Custom Email Service**: Direct integration with email sending service in backend API
- **React Email Templates**: Uses `@repo/email/invite-user-email` for consistent branding
- **Resend Integration**: Configured in backend for reliable email delivery
- **Environment-Aware Links**: Dynamic invitation links based on development/production environment
- **Error Handling**: Email failures are caught and logged without blocking invitation creation
- **Invitation Tracking**: Updates `lastSentAt` timestamp for resend operations

### Performance Optimizations

- **AG Grid Virtualization**: Efficient rendering of large member lists
- **React Query Caching**: Reduces redundant API calls
- **Optimistic Updates**: Immediate UI feedback without waiting for server
- **Server-Side Caching**: React cache() reduces server-side computation

## Rules to adhere to

- crud-operations.mdc
- api-usage-guide.mdc

## API Endpoints

### Member Management

- `GET /api/v1/organizations/{slug}/members` - List organization members
- `PATCH /api/v1/organizations/{slug}/members/{id}` - Update member (role, ban/unban)
- `DELETE /api/v1/organizations/{slug}/members/{id}` - Remove member
- `POST /api/v1/organizations/{slug}/members/{id}/transfer-ownership` - Transfer ownership

### Invitation Management

- `POST /api/v1/organizations/{slug}/invitations` - Create invitations
- `POST /api/v1/organizations/{slug}/invitations/{id}/resend` - Resend invitation
- `DELETE /api/v1/organizations/{slug}/invitations/{id}` - Cancel invitation

### Session Management

- `GET /api/auth/session` - Get current user session

## Reference Documentation

- [Auth.js v5 Documentation](https://authjs.dev/)
- [TanStack Query Documentation](https://tanstack.com/query/latest)
- [AG Grid React Documentation](https://ag-grid.com/react-data-grid/)
- [Hono.js Documentation](https://hono.dev/)
- [Prisma Documentation](https://www.prisma.io/docs)
