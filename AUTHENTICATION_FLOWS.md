# Authentication System Overview

This document describes the complete authentication flows for the Centaly application, which uses a hybrid architecture combining Auth.js (Next.js frontend) with a Hono backend API.

## Architecture Overview

### Frontend (Next.js)

- **Auth.js v5** for session management and database integration
- **Custom server actions** for sign-up, sign-in, and sign-out
- **React Hook Form + Zod** for form validation
- **Custom session utilities** for backend validation

### Backend (Hono)

- **JWT-free approach** - validates Auth.js database sessions directly
- **Session middleware** reads Auth.js session tokens from cookies
- **Database session lookup** for authentication validation
- **Compatible session format** with frontend

### Database

- **Prisma ORM** with Auth.js adapter
- **MySQL/PlanetScale** database
- **Auth.js session tables** (User, Session, VerificationToken)

---

## 1. Sign-Up Flow

### Overview

Users create accounts with email verification before gaining access.

### Files Involved

- **Form**: `apps/dashboard/src/features/auth/components/sign-up-form.tsx`
- **Server Action**: `apps/dashboard/src/features/auth/actions/sign-up.ts`
- **Schema**: `apps/dashboard/src/features/auth/schemas/sign-up-schema.ts`
- **Auth Utilities**: `packages/auth/src/password.ts`, `packages/auth/src/verification.ts`
- **Email Sender**: `packages/email/src/senders/send-verify-email-address-email.ts`
- **Email Template**: `packages/email/src/templates/verify-email.tsx`

### Step-by-Step Flow

#### 1. User Submits Form

```typescript
// sign-up-form.tsx - User fills out form
<form onSubmit={methods.handleSubmit(onSubmit)}>
  // firstName, lastName, email, password fields
  // Note: Supports invitation-based sign-up where email is pre-filled and disabled
</form>

// Invitation handling:
// - Checks URL params for ?invitation={id}
// - Checks sessionStorage for pendingInvitationId/Email
// - Validates invitation via validateInvitation() API call
// - Pre-fills and disables email field when invitation exists
```

#### 2. Server Action Processing

```typescript
// sign-up.ts - signUpWithData() function
1. Validate form data with Zod schema
2. Normalize email to lowercase
3. Check if user already exists
4. Hash password using bcryptjs (13 salt rounds)
5. Create user in database with:
   - name: `${firstName} ${lastName}`
   - email: normalizedEmail
   - password: hashedPassword
   - onboardingStatus: "incomplete"
   - createdAt/updatedAt: new Date()
```

#### 3. Email Verification Setup

```typescript
// sign-up.ts - continues...
6. Generate OTP token using createOtpTokens()
7. Send verification email using sendVerifyEmail() with link: /verify-email/{hashedOtp}
8. Return success response with redirectTo: /verify?email={email}
   Note: Uses redirect() function for form action, returns object for signUpWithData()
```

#### 4. Frontend Response

```typescript
// sign-up-form.tsx - onSubmit handler
if (result.success) {
  router.push(result.redirectTo); // -> /verify?email=...
} else {
  // Handle validation errors and display field-specific messages
  if (result.errors) {
    setFieldErrors(result.errors);
  }
  if (result.message) {
    setError(result.message);
  }
}

// Note: Email sending failure doesn't fail the signup process
// User is still redirected to verify page and can request resend
```

### Database Changes

- **User created** with `emailVerified: null`, `onboardingStatus: "incomplete"`
- **VerificationToken created** with:
  - `identifier`: user email
  - `token`: hashedOtp (SHA-256 hash of `${otp}${AUTH_SECRET}`)
  - `expires`: 720 hours (30 days) from creation

---

## 2. Email Verification Flow

### Overview

Users click email link to verify their email and get automatically signed in.

### Files Involved

- **Email Link Handler**: `apps/dashboard/src/app/(auth)/verify-email/[token]/page.tsx`
- **Server Action**: `apps/dashboard/src/features/auth/actions/verify-email-with-token.ts`
- **Auto-Sign Component**: `apps/dashboard/src/features/auth/components/email-verification-handler.tsx`
- **Backend Endpoint**: `apps/backend/src/routes/auth.ts` (emailVerificationRoute)
- **Auth Utilities**: `packages/auth/src/verification.ts`, `packages/auth/src/encryption.ts`
- **Email Sender**: `packages/email/src/senders/send-welcome-email.ts`
- **Email Template**: `packages/email/src/templates/welcome-email.tsx`

### Step-by-Step Flow

#### 1. User Clicks Email Link

```
Email contains: /verify-email/{hashedOtpToken}
```

#### 2. Token Verification

```typescript
// verify-email-with-token.ts - verifyEmailWithToken()
1. Validate token with Zod schema
2. Find verification token directly using hashed token (no OTP decryption needed)
3. Check if token has expired using isAfter() from date-fns
4. Find user by email from token identifier
5. Check if email is already verified (redirect to sign-in if so)
6. Call verifyEmail() utility to set emailVerified: new Date()
7. Send welcome email using sendWelcomeEmail()
```

#### 3. Encrypted Token Creation

```typescript
// verify-email-with-token.ts - continues...
8. Create encrypted token using symmetricEncrypt() with:
   - userId, email, purpose: "email-verification"
   - expires: 5 minutes from creation
   - Uses AUTH_SECRET for encryption
9. Return success with providerToken for auto-signin
   - redirectUrl: "/onboarding/workspace"
   - providerToken: encrypted token for backend authentication
```

#### 4. Automatic Sign-In Process

```typescript
// email-verification-handler.tsx - useEffect
10. Component receives providerToken from server action
11. Displays "Taking you to onboarding..." with spinner
12. Calls Hono backend: POST /api/auth/signin/email-verification
    - Sends token and callbackUrl in form data
    - Uses credentials: "include" for cross-domain cookies
13. Backend validates encrypted token and creates session
14. Redirects to callbackUrl after 1 second delay
```

#### 5. Backend Session Creation

```typescript
// apps/backend/src/routes/auth.ts - emailVerificationRoute
15. Decrypt token using symmetricDecrypt() with AUTH_SECRET
16. Validate token expiry and purpose
17. Look up user and verify emailVerified is set
18. Generate Auth.js session token (ULID) using generateSessionToken()
19. Create session in database with 30-day expiry
20. Set next-auth.session-token cookie with security flags:
    - HttpOnly, Secure (in production), SameSite=Lax, Path=/, Max-Age=30 days
21. Return success response with user data and callbackUrl
```

### Database Changes

- **User updated**: `emailVerified: new Date()`
- **VerificationToken expired**: `expires` set to epoch (effectively deleted)
- **ChangeEmailRequest deleted**: Any pending email change requests removed
- **ResetPasswordRequest deleted**: Any pending password reset requests removed
- **Auth.js Session created**: `sessionToken`, `userId`, `expires` (30 days)

### Error Handling & Security

#### 1. **Token Validation**

- Validates token format with Zod schema
- Checks token expiry using date-fns
- Handles missing/invalid tokens gracefully
- 5-minute expiry on provider tokens for security

#### 2. **Already Verified Users**

- Redirects to sign-in with "already-verified" message
- Prevents duplicate verification attempts

#### 3. **Encryption Security**

- Uses AES-256 symmetric encryption for provider tokens
- Derives keys using SHA-256 of AUTH_SECRET
- Includes random IV for each encryption operation

#### 4. **Cross-Origin Authentication**

- Handles frontend (port 3000) → backend (port 3001) authentication
- Uses credentials: "include" for cross-domain cookie setting
- Secure cookie flags adapt to environment (development vs production)

#### 5. **Welcome Email**

- Non-blocking welcome email sending
- Verification succeeds even if email fails
- Includes personalized content and onboarding link

---

## 3. Sign-In Flow

### Overview

Existing users authenticate with email/password and get signed in via custom server actions that create Auth.js-compatible database sessions.

### Files Involved

- **Form**: `apps/dashboard/src/features/auth/components/sign-in-form.tsx`
- **Server Actions**: `apps/dashboard/src/features/auth/actions/sign-in.ts`
- **Schema**: `apps/dashboard/src/features/auth/schemas/sign-in-schema.ts`
- **Auth Utilities**: `packages/auth/src/session.ts`, `packages/auth/src/password.ts`
- **Backend Session Validation**: `apps/backend/src/middleware/session-auth.ts`

### Step-by-Step Flow

#### 1. User Submits Credentials

```typescript
// sign-in-form.tsx - React Hook Form with Zod validation
<form onSubmit={methods.handleSubmit(onSubmit)}>
  // email field with autoComplete="username"
  // password field with InputPassword component and autoComplete="current-password"
  // "Forgot your password?" link to /reset-password
</form>

// Form features:
// - Real-time error clearing when user types
// - Field-specific validation errors
// - Loading states during submission
// - Disabled fields during pending state
```

#### 2. Server Action Processing

```typescript
// sign-in.ts - signInWithData() function
1. Validate form data with signInSchema (email format, password min 8 chars)
2. Normalize email to lowercase
3. Find user by email in database with select fields:
   - id, password, email, emailVerified, name
4. Check if user exists and has password
5. Verify password using bcrypt.compare() from @repo/auth/password
6. Check if email is verified (emailVerified field must not be null)
```

#### 3. Session Creation & Cookie Setting

```typescript
// sign-in.ts - continues...
7. Generate Auth.js session token using ulid() from @repo/auth/session
8. Calculate expiry date: 30 days from now using getSessionExpiryFromNow()
9. Create Auth.js database session record:
   - sessionToken: generated ULID
   - userId: user.id
   - expires: 30 days from creation
10. Update user lastLogin timestamp (mimics Auth.js signIn event)
11. Set next-auth.session-token cookie with security flags:
    - HttpOnly: true (XSS protection)
    - Secure: production only (HTTPS)
    - SameSite: "lax" (CSRF protection)
    - Path: "/"
    - Expires: matches session expiry
```

#### 4. Frontend Response Handling

```typescript
// sign-in-form.tsx - onSubmit success/error handling
if (result.success) {
  router.push(result.redirectTo); // -> "/" (root redirect)
} else {
  // Handle validation errors
  if (result.errors) {
    setFieldErrors(result.errors); // Field-specific errors
  }
  if (result.message) {
    setError(result.message); // Generic error message
  }
}

// Error scenarios:
// - "Invalid email or password" (no user enumeration)
// - "Please verify your email before signing in"
// - Validation errors for malformed email/password
// - Generic server error fallback
```

### Database Changes

- **Auth.js Session created**: `sessionToken` (ULID), `userId`, `expires` (30 days from creation)
- **User updated**: `lastLogin: new Date()`

### Security Features

#### 1. **Password Security**

- bcrypt verification with 13 salt rounds (BCRYPT_SALT_LENGTH constant)
- Password minimum 8 characters (client and server validation)
- Generic error messages prevent user enumeration

#### 2. **Session Security**

- ULID-based session tokens (time-sortable, non-predictable)
- 30-day session expiry with database tracking
- HttpOnly cookies prevent XSS attacks
- SameSite=Lax prevents CSRF attacks
- Secure flag in production for HTTPS-only cookies

#### 3. **Email Verification Requirement**

- Users must verify email before signing in
- Clear error message guides unverified users
- Prevents access with unverified accounts

#### 4. **Error Handling**

- No user enumeration: same error for invalid email or password
- Graceful handling of missing user data
- Server error logging without exposing details to client
- Field-specific validation with real-time clearing

---

## 4. Password Reset Flow

### Overview

Users can reset their passwords by requesting a reset link via email and setting a new password. The flow emphasizes security by preventing user enumeration and using single-use time-limited tokens.

### Files Involved

- **Reset Request Form**: `apps/dashboard/src/features/auth/components/reset-password-form.tsx`
- **Reset Request Page**: `apps/dashboard/src/app/(auth)/reset-password/page.tsx`
- **New Password Form**: `apps/dashboard/src/features/auth/components/new-password-form.tsx`
- **New Password Page**: `apps/dashboard/src/app/(auth)/reset-password/new/[requestId]/page.tsx`
- **Server Actions**:
  - `apps/dashboard/src/features/auth/actions/send-reset-password-instructions.ts`
  - `apps/dashboard/src/features/auth/actions/reset-password.ts`
- **Schemas**:
  - `apps/dashboard/src/features/auth/schemas/send-reset-password-instructions-schema.ts`
  - `apps/dashboard/src/features/auth/schemas/new-password-schema.ts`
- **Email Template**: `packages/email/src/templates/reset-password-email.tsx`
- **Email Sender**: `packages/email/src/senders/send-password-reset-email.ts`
- **Auth Utilities**: `packages/auth/src/password.ts`, `packages/auth/src/constants.ts`

### Step-by-Step Flow

#### 1. User Initiates Password Reset

```typescript
// User clicks "Forgot your password?" link in sign-in-form.tsx
<Link href="/reset-password" className="hover:underline cursor-pointer">
  Forgot your password?
</Link>
// Navigates to /reset-password page with ResetPasswordForm component
```

#### 2. User Submits Email Address

```typescript
// reset-password-form.tsx - React Hook Form with Zod validation
<form onSubmit={methods.handleSubmit(onSubmit)}>
  // Single email field with validation
  // Real-time error clearing when user types
  // Loading states and disabled fields during submission
  // "Send reset instructions" button
</form>

// Form features:
// - Email format validation (sendResetPasswordInstructionsSchema)
// - autoComplete="username" for accessibility
// - maxLength={255} for security
// - Clear "Back to sign in" navigation
```

#### 3. Server Action Processing

```typescript
// send-reset-password-instructions.ts - sendResetPasswordInstructions()
1. Validate email with sendResetPasswordInstructionsSchema (email format only)
2. Normalize email to lowercase
3. Find user by email in database (select: name, email only)
4. **Security: Always return success even if user doesn't exist (no enumeration)**
5. If user exists, check for existing unexpired reset requests
6. Reuse existing request OR create new ResetPasswordRequest with:
   - email: user.email
   - expires: addHours(new Date(), PASSWORD_RESET_EXPIRY_HOURS) // 6 hours
   - id: auto-generated ULID
7. Generate reset link: {NEXT_PUBLIC_APP_URL}/reset-password/new/{requestId}
8. Send password reset email via sendPasswordResetEmail()
```

#### 4. Email Sent & Success Message

```typescript
// reset-password-form.tsx - success state (isSubmitted = true)
return (
  <div>
    <h1>Check your email</h1>
    <p>If you have an account, you will receive an email with instructions
       on how to reset your password in a few minutes.</p>
    <Link href="/sign-in">
      <Button variant="link">
        <ArrowLeft /> Back to sign in
      </Button>
    </Link>
  </div>
);

// Note: Same message shown regardless of whether email exists (security)
```

#### 5. Email Template & Delivery

```typescript
// ResetPasswordEmail template features:
// - Professional branded design with Centaly logo
// - Clear "Reset Password" call-to-action button
// - Fallback copy-paste URL option
// - Security disclaimer about ignoring if not requested
// - Tailwind CSS styling with React Email components
// - Both HTML and plain text versions generated

// Email subject: "Reset password instructions"
// Reset link: {baseUrl}/reset-password/new/{requestId}
```

#### 6. User Clicks Email Link & Page Load

```typescript
// /reset-password/new/[requestId] - Next.js dynamic route
// Page extracts requestId from URL params and passes to NewPasswordForm
const NewPasswordForm = ({ requestId }: { requestId: string }) => {
  // Form validates reset request on submission (lazy validation)
  // No upfront API call to check validity (simplified UX)
};
```

#### 7. User Sets New Password

```typescript
// new-password-form.tsx - Dual password form
<form onSubmit={methods.handleSubmit(onSubmit)}>
  // password field with InputPassword component
  // confirmPassword field with matching validation
  // Real-time error clearing
  // Loading states during submission
</form>

// newPasswordSchema validation:
// - password: min 8 characters
// - confirmPassword: min 8 characters
// - .refine() ensures passwords match (error on confirmPassword field)
```

#### 8. Password Reset Completion

```typescript
// reset-password.ts - resetPassword() function
1. Validate password/confirmPassword with newPasswordSchema
2. Find ResetPasswordRequest by requestId (database lookup)
3. Return "Invalid or expired reset link" if not found
4. Check expiry: resetRequest.expires < new Date()
5. If expired: delete request and return "Reset link has expired"
6. Find user by email from resetRequest.email
7. Hash new password using hashPassword() (bcrypt, 13 salt rounds)
8. Update user in database:
   - password: hashedPassword
   - lastLogin: new Date() (treat as fresh sign-in)
9. Delete used ResetPasswordRequest (single-use security)
```

#### 9. Success Redirect & Error Handling

```typescript
// new-password-form.tsx - success/error handling
if (result.success) {
  router.push("/sign-in?message=password-reset-success");
} else {
  // Handle different error types
  if (
    result.message.includes("expired") ||
    result.message.includes("Invalid")
  ) {
    setIsExpired(true); // Show expired link UI
  }
  // Show field errors and general error messages
}

// Expired link UI shows:
// - "Link Expired" heading
// - Clear explanation with recovery options
// - "Request New Reset Link" button → /reset-password
// - "Back to Sign In" fallback link
```

### Database Changes

#### Reset Request Creation:

- **ResetPasswordRequest created**: `id` (ULID), `email`, `expires` (6 hours from creation)

#### Password Reset Completion:

- **User updated**: `password` (bcrypt hashed), `lastLogin: new Date()`
- **ResetPasswordRequest deleted**: Single-use cleanup

### Security Features

#### 1. **Anti-Enumeration Protection**

- Always returns success regardless of email existence
- Same timing and response for valid/invalid emails
- Generic messaging prevents information leakage
- No indication whether user account exists

#### 2. **Token Security**

- ULID-based requestId tokens (time-sortable, non-predictable)
- 6-hour expiry (PASSWORD_RESET_EXPIRY_HOURS constant)
- Single-use tokens (deleted after successful reset)
- Automatic cleanup of expired tokens on access

#### 3. **Rate Limiting & Spam Prevention**

- Reuses existing unexpired reset requests (no duplicates)
- Prevents multiple simultaneous reset attempts
- Reduces email spam and database bloat

#### 4. **Password Security**

- bcrypt hashing with 13 salt rounds (BCRYPT_SALT_LENGTH)
- Password confirmation validation with real-time feedback
- Minimum 8 character requirement (client and server)
- Passwords must match validation using Zod .refine()

#### 5. **Email Security**

- Professional template prevents phishing look-alikes
- Clear security disclaimers in email content
- Both HTML and plain text versions for compatibility
- HTTPS-only reset links in production

### Error Handling

#### 1. **Expired/Invalid Links**

```typescript
// Automatic detection and user-friendly recovery
if (isExpired) {
  return (
    <div>
      <h1>Link Expired</h1>
      <p>This password reset link has expired or is invalid. Please request a new one.</p>
      <Link href="/reset-password">
        <Button>Request New Reset Link</Button>
      </Link>
      <Link href="/sign-in">
        <Button variant="link">
          <ArrowLeft /> Back to Sign In
        </Button>
      </Link>
    </div>
  );
}
```

#### 2. **Validation & User Experience**

- Real-time password confirmation validation
- Field-specific error messages with proper path mapping
- Error clearing when user starts typing (clearErrorsOnChange)
- Loading states prevent double submissions
- Graceful fallbacks for network/server errors

#### 3. **Database & Server Errors**

- Automatic cleanup of expired requests on access
- Proper error logging without exposing sensitive data
- Fallback error messages for unexpected failures
- Transaction safety for password updates

---

## 5. Sign-Out Flow

### Overview

Users can sign out, which clears their session and redirects to sign-in.

### Files Involved

- **Component**: `apps/dashboard/src/features/auth/components/sign-out-button.tsx`
- **Server Action**: `apps/dashboard/src/features/auth/actions/sign-out.ts`

### Step-by-Step Flow

#### 1. User Clicks Sign Out

```typescript
// sign-out-button.tsx - button click
<Button onClick={handleSignOut}>
  Sign out
</Button>
```

#### 2. Session Cleanup

```typescript
// sign-out.ts - signOut() function
1. Get session token from cookies
2. Delete session from database
3. Clear session cookie
4. Redirect to /sign-in
```

### Database Changes

- **Auth.js Session deleted**: Session record removed

---

## 6. Onboarding Flow

### Overview

After email verification and sign-in, users go through a multi-step onboarding process to set up their workspace and optionally invite team members. The flow emphasizes user experience with real-time validation, parallel API calls, and comprehensive error handling.

### Files Involved

- **Workspace Form**: `apps/dashboard/src/features/auth/components/workspace-onboarding-form.tsx`
- **Invite Form**: `apps/dashboard/src/features/auth/components/invite-onboarding-form.tsx`
- **Completion Loader**: `apps/dashboard/src/features/auth/components/onboarding-completion-loader.tsx`
- **API Functions**: `apps/dashboard/src/lib/api/onboarding.ts`
- **Active Organization Hook**: `apps/dashboard/src/hooks/use-active-organization.ts`
- **Backend Endpoints**:
  - `apps/backend/src/routes/organizations.ts` (workspace creation, slug checking)
  - `apps/backend/src/routes/members.ts` (invitation system)
  - `apps/backend/src/routes/users.ts` (onboarding status, active organization)
- **Email Integration**:
  - `packages/email/src/templates/invite-user-email.tsx`
  - `packages/email/src/senders/send-invitation-email.ts`

### Onboarding Status Flow

Users progress through these onboarding statuses:

- `incomplete` → `invite` → `complete` (workspace status is skipped in actual implementation)

### Step-by-Step Flow

#### 1. Initial State Check

```typescript
// After sign-in/email verification, user's onboardingStatus determines routing
if (user.onboardingStatus === "incomplete") {
  // Redirect to workspace creation (no "workspace" status step)
  router.push("/onboarding/workspace");
}
```

#### 2. Workspace Creation (Status: incomplete → invite)

```typescript
// workspace-onboarding-form.tsx - Comprehensive workspace setup
1. User enters workspace name and slug with real-time validation
2. Slug availability check: POST /api/v1/organization/check-slug (pre-submit validation)
3. Frontend validation: workspaceOnboardingSchema with Zod
   - name: min 1 character, required
   - slug: 3-48 characters, regex: ^[a-z0-9]+(?:-[a-z0-9]+)*$
4. Workspace creation: POST /api/v1/organization/create
5. Parallel post-creation operations:
   - updateOnboardingStatus("invite") // Skip "workspace" status
   - updateDefaultWorkspace(slug)
6. Redirect to /onboarding/invite (even if post-operations fail)
```

#### 3. Backend Workspace Creation (Atomic Transaction)

```typescript
// organizations.ts - createOrganizationRoute
1. Database transaction for atomicity:
   a. Check slug uniqueness within transaction (race condition protection)
   b. Update user role to "admin" (organization owner gets admin role)
   c. Create organization with nested membership creation:
      - Organization: name, slug, logo, metadata
      - Membership: userId, organizationId, role="owner"
   d. Set activeOrganizationId in ALL non-expired user sessions
2. Return organization data (id, name, slug, logo, metadata, createdAt)
3. Handle specific errors (slug conflicts → 409, Prisma P2002 → 409)
```

#### 4. Workspace Form UX & Error Handling

```typescript
// workspace-onboarding-form.tsx - Enhanced user experience
1. Real-time error clearing when user types (clearErrorsOnChange)
2. Dynamic URL preview: "app.centaly.com/{slug}"
3. Comprehensive error handling for various scenarios:
   - 401 Unauthorized → "Sign in again"
   - 409 Conflict → "URL already taken"
   - 429 Rate limit → "Wait and try again"
   - 500 Server error → "Technical difficulties"
   - Network errors → "Check connection"
4. Loading states with disabled fields during submission
5. Form reset and cleanup on successful creation
```

#### 5. Team Invitation (Status: invite → complete)

```typescript
// invite-onboarding-form.tsx - Flexible invitation system
1. Up to 3 team member invitations with role selection
2. Role options: viewer, contributor, admin (defaults to contributor)
3. Validation with inviteSchema:
   - Array of 3 invitation objects (email optional, role required)
   - .refine() ensures at least one valid email provided
4. Filter out empty emails before API submission
5. Get current organization: getUserOrganizations() (uses first organization)
6. Send invitations: POST /api/v1/organizations/{slug}/invitations
7. Update onboarding status to "complete"
8. Alternative: Skip option (handleSkip) - still marks complete
```

#### 6. Backend Invitation Processing (Advanced Filtering)

```typescript
// members.ts - createInvitationsRoute
1. Permission validation: Only admin/owner can invite
2. Advanced email filtering:
   - Deduplicate emails within request
   - Check existing members (via Membership table)
   - Check pending invitations (status="pending")
   - Filter out all conflicts
3. Return 400 if all emails already exist/invited
4. Transaction-based invitation creation with:
   - organizationId, email (normalized), role, invitedByUserId
   - token: auto-generated ULID
   - lastSentAt: new Date()
5. Non-blocking email sending (doesn't fail API if email service down)
6. Return success count and invitation details
```

#### 7. Email Invitation System

```typescript
// InviteUserEmail template + sendInvitationEmail sender
1. Professional branded template:
   - Subject: "Organization invitation"
   - Personalized content: "Join {inviterName} on Centaly"
   - Organization context: "{organizationName} workspace"
   - Branded design with Centaly logo and social links
2. Call-to-action: "Accept Invite" button
3. Fallback copy-paste URL option
4. Security disclaimer: "Ignore if unexpected"
5. Both HTML and plain text versions generated
6. Invitation links: {FRONTEND_URL}/accept-invitation/{id}
```

#### 8. Onboarding Completion & Animation

```typescript
// invite-onboarding-form.tsx → OnboardingCompletionLoader
1. Set showCompletionLoader state after successful invite/skip
2. Render OnboardingCompletionLoader component with animations
3. Two-stage completion animation:
   - Stage 1: "Creating account" (1.5 seconds)
   - Stage 2: "Getting ready..." (1.5 seconds)
4. Total 3-second delay before redirect
5. Smart redirection logic using useActiveOrganization hook
```

#### 9. Active Organization Management & Redirection

```typescript
// onboarding-completion-loader.tsx + useActiveOrganization
1. useActiveOrganization hook:
   - Calls getActiveOrganization() API
   - Returns activeOrganization, loading, error, refetch
2. Backend endpoint: GET /api/v1/profile/active-organization
   - Finds most recent non-expired session for user
   - Looks up session.activeOrganizationId
   - Returns organization details with user's role
3. Redirection logic:
   if (activeOrganization?.slug) {
     router.push(`/${activeOrganization.slug}/home`);
   } else {
     router.push("/"); // Fallback to root
   }
```

### Database Changes Throughout Flow

#### Workspace Creation (Single Transaction):

- **User updated**: `role: "admin"` (organization owners get admin role)
- **Organization created**: `name`, `slug`, `logo`, `metadata`, `createdAt`
- **Membership created**: `userId`, `organizationId`, `role: "owner"` (nested creation)
- **Session updated**: `activeOrganizationId` set for ALL non-expired user sessions

#### Post-Creation Updates (Parallel):

- **User updated**: `onboardingStatus: "invite"` (skips "workspace" status)
- **User updated**: `defaultWorkspace: slug`

#### Invitation Creation:

- **Invitation created**: `organizationId`, `email`, `role`, `invitedByUserId`, `token` (ULID), `lastSentAt`, `status: "pending"`
- **User updated**: `onboardingStatus: "complete"`

### Security Features

#### 1. **Slug Validation & Race Condition Protection**

- Client-side: Zod regex validation `^[a-z0-9]+(?:-[a-z0-9]+)*$`
- Pre-submit availability check: POST /api/v1/organization/check-slug
- Transaction-based creation with duplicate check inside transaction
- Prisma P2002 error handling for race conditions
- Length limits: 3-48 characters (validated client and server)

#### 2. **Invitation Security & Conflict Detection**

- ULID-based invitation tokens (time-sortable, non-predictable)
- Permission checks: Only admin/owner can invite
- Advanced email conflict detection:
  - Existing members (via Membership table)
  - Pending invitations (status="pending")
  - Case-insensitive email normalization
- Maximum 10 invitations per request (backend validation)

#### 3. **Session Management & Context**

- Active organization set in ALL user sessions during creation
- Session context persists across frontend/backend
- Non-expired session filtering
- Automatic context updates during organization creation
- Graceful fallback when no active organization

#### 4. **Transaction Safety & Error Recovery**

- Atomic workspace creation (organization + membership + session updates)
- Post-creation operations in parallel (don't block on failures)
- Graceful degradation: redirect to next step even if some operations fail
- Comprehensive error categorization with user-friendly messages

### Error Handling

#### 1. **Workspace Creation Errors**

```typescript
// Comprehensive error mapping in workspace-onboarding-form.tsx
- 401 Unauthorized → "Sign in again"
- 409/slug conflicts → "URL already taken"
- 429 Rate limiting → "Wait and try again"
- 500 Server error → "Technical difficulties"
- Network errors → "Check connection"
- Permission errors → "Contact administrator"
- Plan/subscription limits → "Upgrade plan"
- Generic fallback → Use error message from API
```

#### 2. **Invitation Errors**

- All emails already exist/invited → 400 Bad Request
- Insufficient permissions → 403 Forbidden (admin/owner only)
- Email service failures → Logged, doesn't fail request
- Invalid email formats → Client-side Zod validation
- Organization not found → 404 Not Found

#### 3. **Completion & Redirection Errors**

- Missing active organization → Fallback to root "/"
- useActiveOrganization error handling with retry capability
- Session/authentication errors → Graceful degradation
- Network failures → Error state with refetch option

### User Experience Features

#### 1. **Real-Time Validation & Feedback**

- Dynamic URL preview with live slug updates
- Real-time error clearing when user starts typing
- Pre-submit slug availability checking
- Loading states for all async operations
- Form field disabling during submission

#### 2. **Flexible & Forgiving Flow**

- Skip invitation step entirely (handleSkip option)
- Partial invitation forms (1-3 invitations, empty emails filtered)
- Graceful post-creation operation failures
- Continue flow even if some API calls fail
- Smart role defaulting (contributor)

#### 3. **Professional Email Experience**

- Branded email templates with Centaly design system
- Personalized content with inviter and organization names
- Clear call-to-action buttons with fallback URLs
- Security disclaimers for unexpected invitations
- Both HTML and plain text versions for compatibility

#### 4. **Smooth Transitions & Animation**

- Two-stage completion loader with motion animations
- Progressive disclosure (workspace → invite → complete)
- Context-aware redirection based on active organization
- Consistent loading states and spinner components

### Integration Points

#### 1. **Frontend-Backend Session Synchronization**

```typescript
// Auth.js session token flows seamlessly
Cookie: next-auth.session-token={sessionToken}
// Backend middleware validates same session for context
// activeOrganizationId set in session during creation
```

#### 2. **Organization-Aware Application Architecture**

```typescript
// All authenticated routes access active organization
const { activeOrganization, loading, error } = useActiveOrganization();
// Navigation structure: /{organizationSlug}/{feature}
// Backend endpoints use organization context from session
```

#### 3. **Email Service Integration**

```typescript
// React Email + Resend integration
// Type-safe templates with preview capability
// Non-blocking email sending prevents API failures
// Professional templates prevent phishing look-alikes
```

This onboarding flow provides a comprehensive, secure, and user-friendly experience for workspace setup and team building, with robust error handling, security features, and smooth transitions throughout the process.

---

## Backend Integration

### Session Validation Middleware

**File**: `apps/backend/src/middleware/session-auth.ts`

The backend uses a custom session middleware that validates Auth.js sessions:

```typescript
// Session verification middleware for protected routes
export const sessionAuth = async (c: Context, next: Next) => {
  try {
    // 1. Parse session token from Cookie header using parseSessionTokenFromCookie()
    const cookieHeader = c.req.header("Cookie");
    const token = parseSessionTokenFromCookie(cookieHeader);

    if (!token) {
      // Set null user/session and continue (allows public endpoints)
      c.set("user", null);
      c.set("session", null);
      await next();
      return;
    }

    // 2. Look up Auth.js session in database with user details
    const session = await prisma.session.findUnique({
      where: { sessionToken: token },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            name: true,
            emailVerified: true,
            onboardingStatus: true,
            defaultWorkspace: true,
            role: true,
          },
        },
      },
    });

    // 3. Validate session expiry and user existence
    if (session && session.expires > new Date() && session.user) {
      // Create simplified User object for context
      const user = {
        id: session.user.id,
        email: session.user.email!,
        name: session.user.name!,
      };

      c.set("user", user);
      c.set("session", { user, expires: session.expires });
    } else {
      c.set("user", null);
      c.set("session", null);
    }
  } catch (error) {
    // Graceful error handling - set null and continue
    console.error("Session middleware error:", error);
    c.set("user", null);
    c.set("session", null);
  }

  await next();
};
```

### Protected Routes Configuration

**File**: `apps/backend/src/index.ts`

```typescript
// Apply session authentication middleware to all protected API routes
app.use("/api/v1/*", sessionAuth);

// Auth routes mounted separately (public endpoints)
app.route("/api", authRouter);

// Protected API routes
app.route("/api/v1", api);
```

**Route Structure**:

- `/api/auth/*` - Public authentication endpoints (no middleware)
- `/api/v1/*` - Protected API endpoints (session middleware required)

### Session Token Parsing

**File**: `apps/backend/src/lib/auth-utils.ts`

```typescript
// Parse Auth.js session token from Cookie header
export function parseSessionTokenFromCookie(
  cookieHeader: string | undefined
): string | null {
  if (!cookieHeader) {
    return null;
  }

  // Extract next-auth.session-token from cookie string
  const sessionMatch = cookieHeader.match(/next-auth\.session-token=([^;]+)/);
  return sessionMatch ? sessionMatch[1] || null : null;
}

// Format session cookie with proper security flags
export function formatSessionCookie(
  sessionToken: string,
  isDevelopment: boolean
): string {
  const maxAge = 60 * 60 * 24 * 30; // 30 days
  return `next-auth.session-token=${sessionToken}; HttpOnly; Secure=${!isDevelopment}; SameSite=Lax; Path=/; Max-Age=${maxAge}`;
}
```

### Session Endpoint

**File**: `apps/backend/src/routes/auth.ts`

```typescript
// GET /api/auth/session - Public endpoint for session validation
authRouter.openapi(sessionRoute, async (c) => {
  const cookieHeader = c.req.header("Cookie");
  const token = parseSessionTokenFromCookie(cookieHeader);

  if (!token) {
    return c.json({ user: null }, 200);
  }

  // Same database lookup as middleware
  const session = await prisma.session.findUnique({
    where: { sessionToken: token },
    include: {
      user: {
        select: {
          id: true,
          email: true,
          name: true,
          emailVerified: true,
          onboardingStatus: true,
          defaultWorkspace: true,
          role: true,
        },
      },
    },
  });

  if (session && session.expires > new Date() && session.user) {
    return c.json(
      {
        user: {
          id: session.user.id,
          email: session.user.email!,
          name: session.user.name!,
          emailVerified: session.user.emailVerified?.toISOString() || null,
          onboardingStatus: session.user.onboardingStatus,
          defaultWorkspace: session.user.defaultWorkspace,
          role: session.user.role,
        },
        expires: session.expires.toISOString(),
      },
      200
    );
  } else {
    return c.json({ user: null }, 200);
  }
});
```

### Frontend Session Utilities

**File**: `apps/dashboard/src/features/auth/utils/session.ts`

```typescript
// Custom session validation for Next.js server components
export async function getBackendSession(): Promise<SessionData | null> {
  try {
    // 1. Get next-auth.session-token cookie
    const cookieStore = await cookies();
    const sessionCookie = cookieStore.get("next-auth.session-token");

    if (!sessionCookie) {
      return null;
    }

    // 2. Validate with Hono backend session endpoint
    const response = await fetch("http://localhost:3001/api/auth/session", {
      headers: {
        Cookie: `${sessionCookie.name}=${sessionCookie.value}`,
      },
    });

    if (!response.ok) {
      return null;
    }

    // 3. Return session data with user details
    const sessionData = await response.json();
    return sessionData;
  } catch (error) {
    console.error("Error fetching session from backend:", error);
    return null;
  }
}

// Type guard for session validation
export function hasValidUser(
  session: SessionData | null
): session is SessionData & { user: SessionUser } {
  return session?.user?.id !== undefined;
}
```

### CORS Configuration

**File**: `apps/backend/src/index.ts`

```typescript
// CORS setup for frontend-backend communication
app.use(
  "*",
  cors({
    origin: (origin) => {
      const allowedOrigins = [
        "http://localhost:3000", // Dashboard dev server
        "http://localhost:3001", // API dev server (for Swagger UI)
        "https://app.centaly.com", // Production frontend
        "https://staging.centaly.com", // Staging frontend
        env.FRONTEND_URL || "http://localhost:3000",
      ].filter(Boolean);

      // Allow Vercel preview URLs
      if (origin && origin.endsWith(".vercel.app")) {
        return origin;
      }

      return allowedOrigins.includes(origin || "") ? origin : null;
    },
    allowMethods: ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
    allowHeaders: ["Content-Type", "Authorization", "Cookie"],
    exposeHeaders: ["Content-Length", "X-Request-Id", "Set-Cookie"],
    maxAge: 86400,
    credentials: true, // Essential for cookie-based auth
  })
);
```

---

## Key Design Decisions

### 1. **Unified Session Format**

- Frontend creates Auth.js database sessions
- Backend validates the same Auth.js sessions
- Single source of truth for authentication state

### 2. **Custom Server Actions vs Auth.js Providers**

- **Why Custom**: Better integration with Hono backend
- **Benefits**: Direct control over session creation, error handling, redirects
- **Trade-off**: More code to maintain vs Auth.js built-in flows

### 3. **Database-First Sessions**

- **No JWTs**: Sessions stored in database, tokens are just identifiers
- **Security**: Sessions can be revoked immediately
- **Scalability**: Stateless backend with database lookups

### 4. **Email Verification Pattern**

- **Two-step process**: Verify email → Auto sign-in
- **Backend integration**: Custom Hono endpoint creates sessions
- **User experience**: Seamless transition from email to authenticated state

---

## Security Features

### 1. **Password Security**

- bcrypt hashing with 13 salt rounds
- Password length validation (8+ characters)

### 2. **Session Security**

- HttpOnly cookies (XSS protection)
- Secure flag in production (HTTPS only)
- SameSite=Lax (CSRF protection)
- 30-day expiry with database tracking

### 3. **Email Verification**

- Required before authentication
- Time-based token expiry
- Single-use verification tokens

### 4. **Password Reset Security**

- 6-hour token expiry (configurable via `PASSWORD_RESET_EXPIRY_HOURS`)
- ULID-based tokens (non-predictable)
- Single-use tokens with automatic cleanup
- No user enumeration (always returns success)
- Rate limiting via request reuse
- Secure password confirmation validation

### 5. **Rate Limiting**

- In-memory rate limiter for auth endpoints
- 10 requests per minute per identifier

---

## Error Handling

### 1. **Form Validation**

- Zod schema validation on client and server
- Field-specific error messages
- Real-time error clearing

### 2. **Authentication Errors**

- Generic "invalid email or password" (no user enumeration)
- Email verification required messages
- Server error fallback handling

### 3. **Session Errors**

- Graceful degradation when sessions expire
- Automatic redirect to sign-in when needed
- Error logging without exposing sensitive data

---

## Development Notes

### 1. **Auth.js Event Handlers**

- `signIn` and `signOut` events are commented out
- Custom server actions handle these responsibilities directly
- Events would not trigger since Auth.js providers aren't used

### 2. **Environment Variables**

- `AUTH_SECRET`: Used for token encryption and Auth.js config
- `NEXT_PUBLIC_APP_URL`: For email verification and password reset links
- `PASSWORD_RESET_EXPIRY_HOURS`: Configurable reset token expiry (default: 6 hours)
- Database connection strings for Prisma

### 3. **Type Safety**

- Full TypeScript coverage across auth flows
- Zod schemas provide runtime validation
- Auth.js types for session data structures

This architecture provides a robust, secure, and scalable authentication system that leverages the best of both Auth.js and custom implementation patterns.
