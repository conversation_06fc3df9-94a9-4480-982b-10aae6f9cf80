---
alwaysApply: false
---

# CRUD Operations Guide

This guide provides a comprehensive walkthrough for implementing CRUD (Create, Read, Update, Delete) operations in the Centaly codebase. It follows a three-layer data fetching architecture and demonstrates the patterns established in the codebase.

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [File Structure](#file-structure)
3. [Backend Implementation](#backend-implementation)
4. [Frontend Implementation](#frontend-implementation)
5. [Example Implementation](#example-implementation)
6. [Best Practices](#best-practices)
7. [Common Patterns](#common-patterns)

## Architecture Overview

Our CRUD operations follow a **three-layer data fetching architecture**:

### Layer 1: Server Components (SSR)

- Handle initial data fetching on the server
- Provide fast initial page load with hydrated data
- Use cached DAL functions for optimal performance

### Layer 2: React Query (Client State)

- Manage client-side data caching and synchronization
- Handle refetching, loading states, and error management
- Implement optimistic updates for responsive UX

### Layer 3: Optimistic Updates

- Enable instant UI feedback during mutations
- Automatically rollback on server errors
- Provide seamless user experience

## File Structure

When creating CRUD operations for a new entity, follow this file structure:

```
src/features/{entity}/
├── dal/
│   └── {entity}.ts              # Data Access Layer (server-side)
├── components/                  # Main folder for associated components
├── hooks/
│   └── use-{entity}.ts          # React Query hooks (client-side)
├── schemas/
│   └── {entity}-schema.ts       # Zod validation schemas
└── contexts/ (optional)
    └── {entity}-context.tsx     # React context if needed

apps/backend/src/routes/
└── {entity}.ts                  # Backend API routes

apps/dashboard/src/app/(dashboard)/[slug]/
└── {entity}/
    └── page.tsx                 # Page route component (server component)
```

### Example: Todo Entity Structure

```
src/features/todos/
├── dal/
│   └── todos.ts
├── hooks/
│   └── use-todos.ts
├── components/
│   ├── create-todo-form.tsx
│   ├── todo-item.tsx
│   └── todos-list.tsx
├── schemas/
│   └── todo-schema.ts

apps/backend/src/routes/
└── todos.ts

apps/dashboard/src/app/(dashboard)/[slug]/todos/
└── page.tsx
```

## Backend Implementation

### 1. Database Schema

First, ensure you understand the database schema. Where necessery define new entities in the Prisma schema if it has not already been created:

```prisma
// packages/database/prisma/schema.prisma
model YourEntity {
  id             Int          @id @default(autoincrement())
  name           String
  description    String?
  completed      Boolean      @default(false)
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  userId         String
  user           User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([organizationId])
  @@index([userId, organizationId])
  @@map("your_entities")
}
```

Don't forget to add the relation to User and Organization models:

```prisma
model User {
  // ... existing fields
  yourEntities YourEntity[]
}

model Organization {
  // ... existing fields
  yourEntities YourEntity[]
}
```

### 2. Backend Routes

Create your API routes file:

```typescript
// apps/backend/src/routes/your-entity.ts
import { createRoute, OpenAPIHono, z } from "@hono/zod-openapi";
import { prisma } from "@repo/database/client";

import { schemas, type Variables } from "../lib/openapi.js";
import { notFound, unauthorized } from "../middleware/error-handler.js";

// Response schema (what we return to clients)
const yourEntityResponseSchema = z.object({
  id: z.number().int(),
  name: z.string(),
  description: z.string().nullable(),
  completed: z.boolean(),
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
});

// Create router
export const yourEntityRouter = new OpenAPIHono<{ Variables: Variables }>();

// LIST - Get all entities for organization
const listEntitiesRoute = createRoute({
  method: "get",
  path: "/organizations/{organizationId}/your-entities",
  tags: ["YourEntity"],
  summary: "List entities for organization",
  request: {
    params: z.object({
      organizationId: z.string(),
    }),
    query: z.object({
      page: z.coerce.number().int().positive().default(1).optional(),
      pageSize: z.coerce
        .number()
        .int()
        .positive()
        .max(100)
        .default(20)
        .optional(),
      completed: z.coerce.boolean().optional(),
      search: z.string().optional(),
    }),
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: z.object({
            entities: z.array(yourEntityResponseSchema),
            pagination: schemas.pagination,
          }),
        },
      },
      description: "List of entities",
    },
    401: {
      content: {
        "application/json": {
          schema: schemas.error,
        },
      },
      description: "Unauthorized",
    },
    403: {
      content: {
        "application/json": {
          schema: schemas.error,
        },
      },
      description: "Forbidden",
    },
  },
});

yourEntityRouter.openapi(listEntitiesRoute, async (c) => {
  const user = c.get("user");
  if (!user) {
    return unauthorized("Authentication required");
  }

  const { organizationId } = c.req.param();
  const query = c.req.query();
  const page = Number(query.page) || 1;
  const pageSize = Number(query.pageSize) || 20;

  // Check organization membership
  const membership = await prisma.member.findFirst({
    where: {
      userId: user.id,
      organizationId,
    },
  });

  if (!membership) {
    return c.json(
      { error: "Forbidden", timestamp: new Date().toISOString() },
      403
    );
  }

  // Build where clause
  const whereClause = {
    organizationId,
    ...(query.completed !== undefined && {
      completed: query.completed === "true",
    }),
    ...(query.search && {
      OR: [
        { name: { contains: query.search } },
        { description: { contains: query.search } },
      ],
    }),
  };

  // Get total count
  const total = await prisma.yourEntity.count({ where: whereClause });

  // Get entities with pagination
  const entities = await prisma.yourEntity.findMany({
    where: whereClause,
    orderBy: { createdAt: "desc" },
    skip: (page - 1) * pageSize,
    take: pageSize,
  });

  const formattedEntities = entities.map((entity) => ({
    id: entity.id,
    name: entity.name,
    description: entity.description,
    completed: entity.completed,
    createdAt: entity.createdAt.toISOString(),
    updatedAt: entity.updatedAt.toISOString(),
  }));

  return c.json(
    {
      entities: formattedEntities,
      pagination: {
        page,
        pageSize,
        total,
        totalPages: Math.ceil(total / pageSize),
      },
    },
    200
  );
});

// GET - Get single entity by ID
const getEntityRoute = createRoute({
  method: "get",
  path: "/organizations/{organizationId}/your-entities/{id}",
  tags: ["YourEntity"],
  summary: "Get entity by ID",
  request: {
    params: z.object({
      organizationId: z.string(),
      id: z.coerce.number().int(),
    }),
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: yourEntityResponseSchema,
        },
      },
      description: "Entity found",
    },
    404: {
      content: {
        "application/json": {
          schema: schemas.error,
        },
      },
      description: "Entity not found",
    },
  },
});

yourEntityRouter.openapi(getEntityRoute, async (c) => {
  const user = c.get("user");
  if (!user) {
    return unauthorized("Authentication required");
  }

  const { organizationId, id } = c.req.param();

  // Check organization membership
  const membership = await prisma.member.findFirst({
    where: { userId: user.id, organizationId },
  });

  if (!membership) {
    return c.json(
      { error: "Forbidden", timestamp: new Date().toISOString() },
      403
    );
  }

  const entity = await prisma.yourEntity.findFirst({
    where: { id: Number(id), organizationId },
  });

  if (!entity) {
    return notFound("Entity not found");
  }

  return c.json(
    {
      id: entity.id,
      name: entity.name,
      description: entity.description,
      completed: entity.completed,
      createdAt: entity.createdAt.toISOString(),
      updatedAt: entity.updatedAt.toISOString(),
    },
    200
  );
});

// CREATE - Create new entity
const createEntityRoute = createRoute({
  method: "post",
  path: "/organizations/{organizationId}/your-entities",
  tags: ["YourEntity"],
  summary: "Create new entity",
  request: {
    params: z.object({
      organizationId: z.string(),
    }),
    body: {
      content: {
        "application/json": {
          schema: z.object({
            name: z.string().min(1).max(255),
            description: z.string().max(1000).optional(),
          }),
        },
      },
    },
  },
  responses: {
    201: {
      content: {
        "application/json": {
          schema: yourEntityResponseSchema,
        },
      },
      description: "Entity created",
    },
  },
});

yourEntityRouter.openapi(createEntityRoute, async (c) => {
  const user = c.get("user");
  if (!user) {
    return unauthorized("Authentication required");
  }

  const { organizationId } = c.req.param();
  const { name, description } = c.req.valid("json");

  // Check organization membership
  const membership = await prisma.member.findFirst({
    where: { userId: user.id, organizationId },
  });

  if (!membership) {
    return c.json(
      { error: "Forbidden", timestamp: new Date().toISOString() },
      403
    );
  }

  const entity = await prisma.yourEntity.create({
    data: {
      name,
      description,
      userId: user.id,
      organizationId,
    },
  });

  return c.json(
    {
      id: entity.id,
      name: entity.name,
      description: entity.description,
      completed: entity.completed,
      createdAt: entity.createdAt.toISOString(),
      updatedAt: entity.updatedAt.toISOString(),
    },
    201
  );
});

// UPDATE - Update entity
const updateEntityRoute = createRoute({
  method: "patch",
  path: "/organizations/{organizationId}/your-entities/{id}",
  tags: ["YourEntity"],
  summary: "Update entity",
  request: {
    params: z.object({
      organizationId: z.string(),
      id: z.coerce.number().int(),
    }),
    body: {
      content: {
        "application/json": {
          schema: z.object({
            name: z.string().min(1).max(255).optional(),
            description: z.string().max(1000).optional(),
            completed: z.boolean().optional(),
          }),
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: yourEntityResponseSchema,
        },
      },
      description: "Entity updated",
    },
  },
});

yourEntityRouter.openapi(updateEntityRoute, async (c) => {
  const user = c.get("user");
  if (!user) {
    return unauthorized("Authentication required");
  }

  const { organizationId, id } = c.req.param();
  const updateData = c.req.valid("json");

  // Check organization membership
  const membership = await prisma.member.findFirst({
    where: { userId: user.id, organizationId },
  });

  if (!membership) {
    return c.json(
      { error: "Forbidden", timestamp: new Date().toISOString() },
      403
    );
  }

  // Check if entity exists and belongs to organization
  const existingEntity = await prisma.yourEntity.findFirst({
    where: { id: Number(id), organizationId },
  });

  if (!existingEntity) {
    return notFound("Entity not found");
  }

  // Permission check: users can only update their own entities or if they're admin/owner
  if (
    existingEntity.userId !== user.id &&
    !["admin", "owner"].includes(membership.role)
  ) {
    return c.json(
      { error: "Forbidden", timestamp: new Date().toISOString() },
      403
    );
  }

  const entity = await prisma.yourEntity.update({
    where: { id: Number(id) },
    data: updateData,
  });

  return c.json(
    {
      id: entity.id,
      name: entity.name,
      description: entity.description,
      completed: entity.completed,
      createdAt: entity.createdAt.toISOString(),
      updatedAt: entity.updatedAt.toISOString(),
    },
    200
  );
});

// DELETE - Delete entity
const deleteEntityRoute = createRoute({
  method: "delete",
  path: "/organizations/{organizationId}/your-entities/{id}",
  tags: ["YourEntity"],
  summary: "Delete entity",
  request: {
    params: z.object({
      organizationId: z.string(),
      id: z.coerce.number().int(),
    }),
  },
  responses: {
    204: {
      description: "Entity deleted",
    },
  },
});

yourEntityRouter.openapi(deleteEntityRoute, async (c) => {
  const user = c.get("user");
  if (!user) {
    return unauthorized("Authentication required");
  }

  const { organizationId, id } = c.req.param();

  // Check organization membership
  const membership = await prisma.member.findFirst({
    where: { userId: user.id, organizationId },
  });

  if (!membership) {
    return c.json(
      { error: "Forbidden", timestamp: new Date().toISOString() },
      403
    );
  }

  // Check if entity exists and belongs to organization
  const existingEntity = await prisma.yourEntity.findFirst({
    where: { id: Number(id), organizationId },
  });

  if (!existingEntity) {
    return notFound("Entity not found");
  }

  // Permission check: users can only delete their own entities or if they're admin/owner
  if (
    existingEntity.userId !== user.id &&
    !["admin", "owner"].includes(membership.role)
  ) {
    return c.json(
      { error: "Forbidden", timestamp: new Date().toISOString() },
      403
    );
  }

  await prisma.yourEntity.delete({
    where: { id: Number(id) },
  });

  return c.body(null, 204);
});
```

### 3. Register Routes

Add your router to the main routes file:

```typescript
// apps/backend/src/routes/index.ts
import { yourEntityRouter } from "./your-entity.js";

export function registerRoutes(api: OpenAPIHono) {
  // ... existing routes
  api.route("/", yourEntityRouter);
}
```

## Frontend Implementation

### 1. Data Access Layer (DAL)

Create server-side data fetching functions:

```typescript
// src/features/your-entity/dal/your-entity.ts
import { cache } from "react";
import { headers } from "next/headers";

// Types
export interface YourEntity {
  id: number;
  name: string;
  description: string | null;
  completed: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CreateEntityData {
  name: string;
  description?: string;
}

export interface UpdateEntityData {
  name?: string;
  description?: string;
  completed?: boolean;
}

interface EntitiesResponse {
  entities: YourEntity[];
  pagination: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
}

// Get entities for organization with caching
export const getEntities = cache(
  async (
    organizationId: string,
    options?: {
      page?: number;
      pageSize?: number;
      completed?: boolean;
      search?: string;
    }
  ): Promise<EntitiesResponse> => {
    try {
      const headersList = await headers();

      const searchParams = new URLSearchParams();
      if (options?.page) searchParams.set("page", options.page.toString());
      if (options?.pageSize)
        searchParams.set("pageSize", options.pageSize.toString());
      if (options?.completed !== undefined)
        searchParams.set("completed", options.completed.toString());
      if (options?.search) searchParams.set("search", options.search);

      const url = `${process.env.NEXT_PUBLIC_API_URL || "http://localhost:3001"}/api/v1/organizations/${organizationId}/your-entities${searchParams.toString() ? `?${searchParams.toString()}` : ""}`;

      const response = await fetch(url, {
        headers: {
          Cookie: headersList.get("cookie") || "",
          "Content-Type": "application/json",
        },
        cache: "no-store",
        credentials: "include",
      });

      if (!response.ok) {
        console.error(
          `Failed to fetch entities: ${response.status} ${response.statusText}`
        );
        return {
          entities: [],
          pagination: {
            page: options?.page || 1,
            pageSize: options?.pageSize || 20,
            total: 0,
            totalPages: 0,
          },
        };
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error("Failed to fetch entities:", error);
      return {
        entities: [],
        pagination: {
          page: options?.page || 1,
          pageSize: options?.pageSize || 20,
          total: 0,
          totalPages: 0,
        },
      };
    }
  }
);

// Get single entity by ID
export const getEntity = cache(
  async (
    organizationId: string,
    entityId: number
  ): Promise<YourEntity | null> => {
    try {
      const headersList = await headers();

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL || "http://localhost:3001"}/api/v1/organizations/${organizationId}/your-entities/${entityId}`,
        {
          headers: {
            Cookie: headersList.get("cookie") || "",
            "Content-Type": "application/json",
          },
          cache: "no-store",
          credentials: "include",
        }
      );

      if (!response.ok) {
        if (response.status === 404) {
          return null;
        }
        console.error(
          `Failed to fetch entity: ${response.status} ${response.statusText}`
        );
        return null;
      }

      const entity = await response.json();
      return entity;
    } catch (error) {
      console.error("Failed to fetch entity:", error);
      return null;
    }
  }
);

// Create new entity
export async function createEntity(
  organizationId: string,
  data: CreateEntityData
): Promise<YourEntity | null> {
  try {
    const headersList = await headers();

    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_URL || "http://localhost:3001"}/api/v1/organizations/${organizationId}/your-entities`,
      {
        method: "POST",
        headers: {
          Cookie: headersList.get("cookie") || "",
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
        credentials: "include",
      }
    );

    if (!response.ok) {
      console.error(
        `Failed to create entity: ${response.status} ${response.statusText}`
      );
      return null;
    }

    const entity = await response.json();
    return entity;
  } catch (error) {
    console.error("Failed to create entity:", error);
    return null;
  }
}

// Update entity
export async function updateEntity(
  organizationId: string,
  entityId: number,
  data: UpdateEntityData
): Promise<YourEntity | null> {
  try {
    const headersList = await headers();

    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_URL || "http://localhost:3001"}/api/v1/organizations/${organizationId}/your-entities/${entityId}`,
      {
        method: "PATCH",
        headers: {
          Cookie: headersList.get("cookie") || "",
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
        credentials: "include",
      }
    );

    if (!response.ok) {
      console.error(
        `Failed to update entity: ${response.status} ${response.statusText}`
      );
      return null;
    }

    const entity = await response.json();
    return entity;
  } catch (error) {
    console.error("Failed to update entity:", error);
    return null;
  }
}

// Delete entity
export async function deleteEntity(
  organizationId: string,
  entityId: number
): Promise<boolean> {
  try {
    const headersList = await headers();

    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_URL || "http://localhost:3001"}/api/v1/organizations/${organizationId}/your-entities/${entityId}`,
      {
        method: "DELETE",
        headers: {
          Cookie: headersList.get("cookie") || "",
          "Content-Type": "application/json",
        },
        credentials: "include",
      }
    );

    if (!response.ok) {
      console.error(
        `Failed to delete entity: ${response.status} ${response.statusText}`
      );
      return false;
    }

    return true;
  } catch (error) {
    console.error("Failed to delete entity:", error);
    return false;
  }
}
```

### 2. React Query Hooks

Create client-side hooks for data management:

```typescript
// src/features/your-entity/hooks/use-your-entity.ts
"use client";

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import type {
  YourEntity,
  CreateEntityData,
  UpdateEntityData,
} from "../dal/your-entity";

// Client-side API functions for mutations
async function createEntityClient(
  organizationId: string,
  data: CreateEntityData
): Promise<YourEntity> {
  const response = await fetch(
    `${process.env.NEXT_PUBLIC_API_URL || "http://localhost:3001"}/api/v1/organizations/${organizationId}/your-entities`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
      credentials: "include",
    }
  );

  if (!response.ok) {
    throw new Error(
      `Failed to create entity: ${response.status} ${response.statusText}`
    );
  }

  return response.json();
}

async function updateEntityClient(
  organizationId: string,
  entityId: number,
  data: UpdateEntityData
): Promise<YourEntity> {
  const response = await fetch(
    `${process.env.NEXT_PUBLIC_API_URL || "http://localhost:3001"}/api/v1/organizations/${organizationId}/your-entities/${entityId}`,
    {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
      credentials: "include",
    }
  );

  if (!response.ok) {
    throw new Error(
      `Failed to update entity: ${response.status} ${response.statusText}`
    );
  }

  return response.json();
}

async function deleteEntityClient(
  organizationId: string,
  entityId: number
): Promise<void> {
  const response = await fetch(
    `${process.env.NEXT_PUBLIC_API_URL || "http://localhost:3001"}/api/v1/organizations/${organizationId}/your-entities/${entityId}`,
    {
      method: "DELETE",
      credentials: "include",
    }
  );

  if (!response.ok) {
    throw new Error(
      `Failed to delete entity: ${response.status} ${response.statusText}`
    );
  }
}

async function fetchEntities(
  organizationId: string,
  options?: {
    page?: number;
    pageSize?: number;
    completed?: boolean;
    search?: string;
  }
) {
  const searchParams = new URLSearchParams();
  if (options?.page) searchParams.set("page", options.page.toString());
  if (options?.pageSize)
    searchParams.set("pageSize", options.pageSize.toString());
  if (options?.completed !== undefined)
    searchParams.set("completed", options.completed.toString());
  if (options?.search) searchParams.set("search", options.search);

  const url = `${process.env.NEXT_PUBLIC_API_URL || "http://localhost:3001"}/api/v1/organizations/${organizationId}/your-entities${searchParams.toString() ? `?${searchParams.toString()}` : ""}`;

  const response = await fetch(url, {
    credentials: "include",
  });

  if (!response.ok) {
    throw new Error(
      `Failed to fetch entities: ${response.status} ${response.statusText}`
    );
  }

  return response.json();
}

// Hook for fetching entities with React Query
export function useEntities(
  organizationId: string,
  options?: {
    page?: number;
    pageSize?: number;
    completed?: boolean;
    search?: string;
  },
  initialData?: { entities: YourEntity[]; pagination: any }
) {
  return useQuery({
    queryKey: ["entities", organizationId, options],
    queryFn: () => fetchEntities(organizationId, options),
    initialData,
    staleTime: 30000, // 30 seconds
    refetchOnWindowFocus: false,
  });
}

// Hook for creating entities
export function useCreateEntity(organizationId: string) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateEntityData) =>
      createEntityClient(organizationId, data),
    onSuccess: () => {
      // Invalidate and refetch entities
      queryClient.invalidateQueries({ queryKey: ["entities", organizationId] });
      toast.success("Entity created successfully");
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to create entity");
    },
  });
}

// Hook for updating entities with optimistic updates
export function useUpdateEntity(organizationId: string) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      entityId,
      data,
    }: {
      entityId: number;
      data: UpdateEntityData;
    }) => updateEntityClient(organizationId, entityId, data),
    onMutate: async ({ entityId, data }) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({
        queryKey: ["entities", organizationId],
      });

      // Snapshot the previous value
      const previousEntities = queryClient.getQueryData([
        "entities",
        organizationId,
      ]);

      // Optimistically update to the new value
      queryClient.setQueryData(
        ["entities", organizationId],
        (old: { entities: YourEntity[]; pagination: any } | undefined) => {
          if (!old?.entities) return old;

          return {
            ...old,
            entities: old.entities.map((entity: YourEntity) =>
              entity.id === entityId
                ? {
                    ...entity,
                    ...data,
                    updatedAt: new Date().toISOString(),
                  }
                : entity
            ),
          };
        }
      );

      // Return a context object with the snapshotted value
      return { previousEntities };
    },
    onError: (error: Error, variables, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousEntities) {
        queryClient.setQueryData(
          ["entities", organizationId],
          context.previousEntities
        );
      }
      toast.error(error.message || "Failed to update entity");
    },
    onSettled: () => {
      // Always refetch after error or success
      queryClient.invalidateQueries({ queryKey: ["entities", organizationId] });
    },
  });
}

// Hook for deleting entities with optimistic updates
export function useDeleteEntity(organizationId: string) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (entityId: number) =>
      deleteEntityClient(organizationId, entityId),
    onMutate: async (entityId) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({
        queryKey: ["entities", organizationId],
      });

      // Snapshot the previous value
      const previousEntities = queryClient.getQueryData([
        "entities",
        organizationId,
      ]);

      // Optimistically update to the new value
      queryClient.setQueryData(
        ["entities", organizationId],
        (old: { entities: YourEntity[]; pagination: any } | undefined) => {
          if (!old?.entities) return old;

          return {
            ...old,
            entities: old.entities.filter(
              (entity: YourEntity) => entity.id !== entityId
            ),
            pagination: {
              ...old.pagination,
              total: old.pagination.total - 1,
            },
          };
        }
      );

      // Return a context object with the snapshotted value
      return { previousEntities };
    },
    onError: (error: Error, variables, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousEntities) {
        queryClient.setQueryData(
          ["entities", organizationId],
          context.previousEntities
        );
      }
      toast.error(error.message || "Failed to delete entity");
    },
    onSuccess: () => {
      toast.success("Entity deleted successfully");
    },
    onSettled: () => {
      // Always refetch after error or success
      queryClient.invalidateQueries({ queryKey: ["entities", organizationId] });
    },
  });
}
```

### 3. Validation Schemas

Create Zod schemas for form validation:

```typescript
// src/features/your-entity/schemas/your-entity-schema.ts
import { z } from "zod";

// Schema for creating a new entity
export const createEntitySchema = z.object({
  name: z
    .string()
    .min(1, "Name is required")
    .max(255, "Name must be less than 255 characters")
    .trim(),
  description: z
    .string()
    .max(1000, "Description must be less than 1000 characters")
    .optional(),
});

// Schema for updating an entity
export const updateEntitySchema = z.object({
  name: z
    .string()
    .min(1, "Name is required")
    .max(255, "Name must be less than 255 characters")
    .trim()
    .optional(),
  description: z
    .string()
    .max(1000, "Description must be less than 1000 characters")
    .optional(),
  completed: z.boolean().optional(),
});

// Infer types from schemas
export type CreateEntityFormData = z.infer<typeof createEntitySchema>;
export type UpdateEntityFormData = z.infer<typeof updateEntitySchema>;
```

### 4. Example Components

#### Example Form Component

```typescript
// src/features/your-entity/components/create-entity-form.tsx
"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Plus } from "lucide-react";

import { Button } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import { Textarea } from "@repo/ui/components/textarea";
import { cn } from "@repo/ui/utils/cn";

import { useCreateEntity } from "../hooks/use-your-entity";
import { createEntitySchema, type CreateEntityFormData } from "../schemas/your-entity-schema";

interface CreateEntityFormProps {
  organizationId: string;
}

export function CreateEntityForm({ organizationId }: CreateEntityFormProps) {
  const createEntityMutation = useCreateEntity(organizationId);

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors }
  } = useForm<CreateEntityFormData>({
    resolver: zodResolver(createEntitySchema)
  });

  const onSubmit = (data: CreateEntityFormData) => {
    createEntityMutation.mutate(data, {
      onSuccess: () => {
        reset();
      }
    });
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      <div>
        <Input
          {...register("name")}
          placeholder="Entity name..."
          className={cn(
            errors.name && "border-red-500"
          )}
          disabled={createEntityMutation.isPending}
        />
        {errors.name && (
          <p className="text-sm text-red-500 mt-1">{errors.name.message}</p>
        )}
      </div>

      <div>
        <Textarea
          {...register("description")}
          placeholder="Description (optional)..."
          className={cn(
            errors.description && "border-red-500"
          )}
          disabled={createEntityMutation.isPending}
        />
        {errors.description && (
          <p className="text-sm text-red-500 mt-1">{errors.description.message}</p>
        )}
      </div>

      <Button
        type="submit"
        disabled={createEntityMutation.isPending}
        className="flex items-center gap-2"
      >
        <Plus className="h-4 w-4" />
        Create Entity
      </Button>
    </form>
  );
}
```

#### Example Entity Item Component

```typescript
// src/features/your-entity/components/entity-item.tsx
"use client";

import { useState } from "react";
import { Trash2, Edit3, Check, X } from "lucide-react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";

import { Button } from "@repo/ui/components/button";
import { Checkbox } from "@repo/ui/components/checkbox";
import { Input } from "@repo/ui/components/input";
import { Textarea } from "@repo/ui/components/textarea";
import { cn } from "@repo/ui/utils/cn";

import type { YourEntity } from "../dal/your-entity";
import { useUpdateEntity, useDeleteEntity } from "../hooks/use-your-entity";
import { updateEntitySchema, type UpdateEntityFormData } from "../schemas/your-entity-schema";

interface EntityItemProps {
  entity: YourEntity;
  organizationId: string;
}

export function EntityItem({ entity, organizationId }: EntityItemProps) {
  const [isEditing, setIsEditing] = useState(false);
  const updateEntityMutation = useUpdateEntity(organizationId);
  const deleteEntityMutation = useDeleteEntity(organizationId);

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors }
  } = useForm<UpdateEntityFormData>({
    resolver: zodResolver(updateEntitySchema),
    defaultValues: {
      name: entity.name,
      description: entity.description || ""
    }
  });

  const handleToggleComplete = () => {
    updateEntityMutation.mutate({
      entityId: entity.id,
      data: { completed: !entity.completed }
    });
  };

  const handleDelete = () => {
    if (window.confirm("Are you sure you want to delete this entity?")) {
      deleteEntityMutation.mutate(entity.id);
    }
  };

  const handleStartEdit = () => {
    reset({ name: entity.name, description: entity.description || "" });
    setIsEditing(true);
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
    reset({ name: entity.name, description: entity.description || "" });
  };

  const handleSaveEdit = (data: UpdateEntityFormData) => {
    updateEntityMutation.mutate({
      entityId: entity.id,
      data
    }, {
      onSuccess: () => {
        setIsEditing(false);
      }
    });
  };

  const isLoading = updateEntityMutation.isPending || deleteEntityMutation.isPending;

  return (
    <div className={cn(
      "flex items-start gap-3 p-4 border rounded-lg bg-white",
      isLoading && "opacity-50 pointer-events-none"
    )}>
      <Checkbox
        checked={entity.completed}
        onCheckedChange={handleToggleComplete}
        disabled={isLoading}
        className="mt-1"
      />

      <div className="flex-1 min-w-0">
        {isEditing ? (
          <form onSubmit={handleSubmit(handleSaveEdit)} className="space-y-3">
            <div>
              <Input
                {...register("name")}
                placeholder="Entity name..."
                className={cn(
                  "h-8",
                  errors.name && "border-red-500"
                )}
                autoFocus
              />
              {errors.name && (
                <p className="text-sm text-red-500 mt-1">{errors.name.message}</p>
              )}
            </div>
            <div>
              <Textarea
                {...register("description")}
                placeholder="Description..."
                className={cn(
                  "min-h-16",
                  errors.description && "border-red-500"
                )}
              />
              {errors.description && (
                <p className="text-sm text-red-500 mt-1">{errors.description.message}</p>
              )}
            </div>
            <div className="flex items-center gap-2">
              <Button
                type="submit"
                size="sm"
                disabled={updateEntityMutation.isPending}
                className="flex items-center gap-1"
              >
                <Check className="h-4 w-4" />
                Save
              </Button>
              <Button
                type="button"
                size="sm"
                variant="outline"
                onClick={handleCancelEdit}
              >
                <X className="h-4 w-4" />
                Cancel
              </Button>
            </div>
          </form>
        ) : (
          <div className="space-y-2">
            <h3 className={cn(
              "text-sm font-medium break-words",
              entity.completed && "text-gray-500 line-through"
            )}>
              {entity.name}
            </h3>
            {entity.description && (
              <p className={cn(
                "text-sm text-gray-600 break-words",
                entity.completed && "text-gray-400 line-through"
              )}>
                {entity.description}
              </p>
            )}
          </div>
        )}
      </div>

      {!isEditing && (
        <div className="flex items-center gap-1">
          <Button
            size="sm"
            variant="ghost"
            className="h-8 w-8 p-0"
            onClick={handleStartEdit}
            disabled={isLoading}
          >
            <Edit3 className="h-4 w-4" />
          </Button>
          <Button
            size="sm"
            variant="ghost"
            className="h-8 w-8 p-0 text-red-500 hover:text-red-700"
            onClick={handleDelete}
            disabled={isLoading}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      )}
    </div>
  );
}
```

#### Example Entity List Component

```typescript
// src/features/your-entity/components/entities-list.tsx
"use client";

import { useState } from "react";
import { Filter, Search } from "lucide-react";

import { Button } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@repo/ui/components/select";

import type { YourEntity } from "../dal/your-entity";
import { useEntities } from "../hooks/use-your-entity";
import { EntityItem } from "./entity-item";

interface EntitiesListProps {
  organizationId: string;
  initialData?: {
    entities: YourEntity[];
    pagination: {
      page: number;
      pageSize: number;
      total: number;
      totalPages: number;
    };
  };
}

export function EntitiesList({ organizationId, initialData }: EntitiesListProps) {
  const [filter, setFilter] = useState<"all" | "active" | "completed">("all");
  const [search, setSearch] = useState("");
  const [page, setPage] = useState(1);

  const filterOptions = {
    all: undefined,
    active: false,
    completed: true
  };

  const { data, isLoading, error } = useEntities(
    organizationId,
    {
      page,
      pageSize: 20,
      completed: filterOptions[filter],
      search: search || undefined
    },
    initialData
  );

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-red-500">Failed to load entities. Please try again.</p>
      </div>
    );
  }

  if (isLoading && !data) {
    return (
      <div className="space-y-3">
        {Array.from({ length: 3 }).map((_, i) => (
          <div key={i} className="h-20 bg-gray-100 animate-pulse rounded-lg" />
        ))}
      </div>
    );
  }

  const entities = data?.entities || [];
  const pagination = data?.pagination;

  return (
    <div className="space-y-4">
      {/* Filter and Search Controls */}
      <div className="flex items-center gap-4 flex-wrap">
        <div className="flex items-center gap-2">
          <Filter className="h-4 w-4 text-gray-500" />
          <Select value={filter} onValueChange={(value) => {
            setFilter(value as "all" | "active" | "completed");
            setPage(1); // Reset to first page when filter changes
          }}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All</SelectItem>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="completed">Completed</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex items-center gap-2 flex-1 max-w-sm">
          <Search className="h-4 w-4 text-gray-500" />
          <Input
            placeholder="Search entities..."
            value={search}
            onChange={(e) => {
              setSearch(e.target.value);
              setPage(1); // Reset to first page when search changes
            }}
            className="h-9"
          />
        </div>

        {pagination && (
          <div className="text-sm text-gray-500">
            {pagination.total} {pagination.total === 1 ? "entity" : "entities"}
          </div>
        )}
      </div>

      {/* Entities List */}
      {entities.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-gray-500">
            {search
              ? `No entities found matching "${search}"`
              : filter === "all"
              ? "No entities yet. Create your first entity above!"
              : filter === "active"
              ? "No active entities. Great job!"
              : "No completed entities yet."
            }
          </p>
        </div>
      ) : (
        <div className="space-y-3">
          {entities.map((entity) => (
            <EntityItem
              key={entity.id}
              entity={entity}
              organizationId={organizationId}
            />
          ))}
        </div>
      )}

      {/* Pagination */}
      {pagination && pagination.totalPages > 1 && (
        <div className="flex items-center justify-center gap-2 pt-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setPage(page - 1)}
            disabled={page === 1 || isLoading}
          >
            Previous
          </Button>

          <span className="text-sm text-gray-500">
            Page {page} of {pagination.totalPages}
          </span>

          <Button
            variant="outline"
            size="sm"
            onClick={() => setPage(page + 1)}
            disabled={page === pagination.totalPages || isLoading}
          >
            Next
          </Button>
        </div>
      )}
    </div>
  );
}
```

### 5. Example Page Component

Create the main page server component:

```typescript
// src/app/(dashboard)/[slug]/your-entities/page.tsx
import { Suspense } from "react";
import { Metadata } from "next";

import { WorkspaceDAL } from "@/src/features/auth/dal/workspace-dal";
import { getEntities } from "../../../../features/your-entity/dal/your-entity";
import { CreateEntityForm } from "../../../../features/your-entity/components/create-entity-form";
import { EntitiesList } from "../../../../features/your-entity/components/entities-list";

interface EntitiesPageProps {
  params: Promise<{
    slug: string;
  }>;
  searchParams: Promise<{
    page?: string;
    completed?: string;
    search?: string;
  }>;
}

export async function generateMetadata({
  params
}: {
  params: Promise<{ slug: string }>;
}): Promise<Metadata> {
  const { slug } = await params;

  return {
    title: `Your Entities - ${slug}`,
    description: "Manage your entities and track your progress"
  };
}

function EntitiesLoading() {
  return (
    <div className="space-y-6">
      <div className="h-12 bg-gray-100 animate-pulse rounded-lg" />
      <div className="space-y-3">
        {Array.from({ length: 5 }).map((_, i) => (
          <div key={i} className="h-20 bg-gray-100 animate-pulse rounded-lg" />
        ))}
      </div>
    </div>
  );
}

async function EntitiesContent({
  slug,
  searchParams
}: {
  slug: string;
  searchParams: { page?: string; completed?: string; search?: string };
}) {
  // Validate workspace access and get workspace data
  await WorkspaceDAL.validateWorkspaceAccess(slug);
  const currentWorkspace = await WorkspaceDAL.getValidatedWorkspace(slug);

  if (!currentWorkspace) {
    // This should not happen after validation, but handle gracefully
    throw new Error(`Workspace not found: ${slug}`);
  }

  // Parse search params
  const page = searchParams.page ? parseInt(searchParams.page, 10) : 1;
  const completed = searchParams.completed === "true" ? true :
                   searchParams.completed === "false" ? false : undefined;
  const search = searchParams.search || undefined;

  // Fetch initial entities data on server
  const initialData = await getEntities(currentWorkspace.id, {
    page,
    pageSize: 20,
    completed,
    search
  });

  return (
    <div className="space-y-6 p-10">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Your Entities</h1>
        <p className="text-gray-600 mt-1">
          Manage your entities and track your progress
        </p>
      </div>

      {/* Create Entity Form */}
      <div className="bg-white p-6 rounded-lg border">
        <h2 className="text-lg font-semibold mb-4">Create New Entity</h2>
        <CreateEntityForm organizationId={currentWorkspace.id} />
      </div>

      {/* Entities List */}
      <div className="bg-white p-6 rounded-lg border">
        <h2 className="text-lg font-semibold mb-4">Your Entities</h2>
        <EntitiesList
          organizationId={currentWorkspace.id}
          initialData={initialData}
        />
      </div>
    </div>
  );
}

export default async function EntitiesPage({ params, searchParams }: EntitiesPageProps) {
  const { slug } = await params;
  const resolvedSearchParams = await searchParams;

  return (
    <Suspense fallback={<EntitiesLoading />}>
      <EntitiesContent slug={slug} searchParams={resolvedSearchParams} />
    </Suspense>
  );
}
```

## Example Implementation

This guide was demonstrated with the **Todo** entity implementation. You can reference the following files for a complete working example:

- Backend: `apps/backend/src/routes/todos.ts`
- DAL: `src/features/todos/dal/todos.ts`
- Hooks: `src/features/todos/hooks/use-todos.ts`
- Components: `src/features/todos/components/*`
- Schemas: `src/features/todos/schemas/todo-schema.ts`
- Page: `src/app/(dashboard)/[slug]/todos/page.tsx`

## Best Practices

### 1. Authentication & Authorization

- ✅ Always check user authentication with `c.get("user")`
- ✅ Verify organization membership for all operations
- ✅ Implement proper permission checks (own data vs admin/owner)
- ✅ Use consistent error responses with timestamps

### 2. Data Validation

- ✅ Use Zod schemas for both backend and frontend validation
- ✅ Validate all input parameters (params, query, body)
- ✅ Set reasonable limits (string lengths, numbers ranges)
- ✅ Handle edge cases gracefully

### 3. Error Handling

- ✅ Use try-catch blocks for all async operations
- ✅ Log errors with context for debugging
- ✅ Return user-friendly error messages
- ✅ Handle network failures gracefully

### 4. Performance Optimization

- ✅ Use React `cache()` for server-side data fetching
- ✅ Implement proper database indexing
- ✅ Use pagination for large datasets
- ✅ Set appropriate stale times in React Query

### 5. User Experience

- ✅ Implement optimistic updates for immediate feedback
- ✅ Show loading states during operations
- ✅ Provide clear success/error feedback via toasts
- ✅ Include confirmation dialogs for destructive actions

### 6. Code Organization

- ✅ Follow the established file structure consistently
- ✅ Use descriptive names for functions and variables
- ✅ Keep components focused and reusable
- ✅ Separate concerns between DAL, hooks, and components

## Common Patterns

### 1. Organization-Scoped Resources

All resources should be scoped to organizations and include membership checks:

```typescript
// Check organization membership
const membership = await prisma.Membership.findFirst({
  where: { userId: user.id, organizationId },
});

if (!membership) {
  return c.json(
    { error: "Forbidden", timestamp: new Date().toISOString() },
    403
  );
}
```

### 2. User Ownership Validation

For user-specific resources, check ownership or admin privileges:

```typescript
// Permission check: users can only modify their own data or if they're admin/owner
if (
  existingEntity.userId !== user.id &&
  !["admin", "owner"].includes(membership.role)
) {
  return c.json(
    { error: "Forbidden", timestamp: new Date().toISOString() },
    403
  );
}
```

### 3. Consistent Error Responses

Always include timestamps in error responses:

```typescript
return c.json(
  {
    error: "Error message",
    timestamp: new Date().toISOString(),
  },
  statusCode
);
```

### 4. Database Queries with Filtering

Build dynamic where clauses for flexible filtering:

```typescript
const whereClause = {
  organizationId,
  ...(completed !== undefined && { completed }),
  ...(search && {
    OR: [{ name: { contains: search } }, { description: { contains: search } }],
  }),
};
```

### 5. Optimistic Updates Pattern

Always include snapshot, optimistic update, and rollback logic:

```typescript
onMutate: async (updateData) => {
  await queryClient.cancelQueries({ queryKey: ["entities", organizationId] });
  const previousData = queryClient.getQueryData(["entities", organizationId]);

  queryClient.setQueryData(["entities", organizationId], (old) => {
    // Optimistic update logic
  });

  return { previousData };
},
onError: (error, variables, context) => {
  if (context?.previousData) {
    queryClient.setQueryData(["entities", organizationId], context.previousData);
  }
  toast.error(error.message);
}
```

## Conclusion

This guide provides a complete pattern for implementing CRUD operations in the Centaly codebase. By following these patterns, you ensure:

- **Consistency** across all features
- **Security** through proper authentication and authorization
- **Performance** via caching and optimizations
- **User Experience** through optimistic updates and proper feedback
- **Maintainability** through organized code structure

Always reference the Todo implementation as a working example and adapt the patterns to your specific entity requirements.
